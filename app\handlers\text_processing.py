import logging
import time
from typing import List
from aiogram import Router, <PERSON>, <PERSON><PERSON>, Dispatcher # <--- До<PERSON>а<PERSON><PERSON><PERSON>н Dispatcher
from aiogram.types import Message, CallbackQuery
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.exceptions import TelegramBadRequest
from aiogram.enums import ParseMode
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.types import InlineKeyboardButton
from app.localization import get_text
from app.text_messages import llm_response
from app.supabase.messages import save_message, delete_user_messages
from app.subscription_manager import SubscriptionManager
from app.supabase.users import get_user_by_tg_id, get_user_model, get_user_language, set_user_model
from config.models_config import MODELS_CONFIG
from app.utils.request_limiter import check_limits
from app.utils.error_handler import handle_error
from app.utils.formatting import convert_markdown_to_html

router = Router()
logger = logging.getLogger(__name__)

class Generate(StatesGroup):
    text = State()

# Добавляем новый обработчик для команды /model
@router.message(Command("model"))
async def model_command(message: Message):
    """Обрабатывает команду /model для выбора модели для генерации текста"""
    user_language = await get_user_language(message.from_user.id)
    select_model_message = get_text("menu", "select_model", user_language)

    # Получаем текущую модель пользователя
    current_model = await get_user_model(message.from_user.id)

    # Создаем динамическую клавиатуру
    builder = InlineKeyboardBuilder()

    # Добавляем кнопки моделей с галочкой для текущей
    for model_id, model_info in MODELS_CONFIG.items():
        button_text = f"✅ {model_info['name']}" if model_id == current_model else model_info['name']
        builder.add(InlineKeyboardButton(text=button_text, callback_data=f"model_{model_id}"))

    # Настраиваем компоновку (по одной кнопке в строке)
    builder.adjust(1)

    await message.answer(
        select_model_message,
        reply_markup=builder.as_markup(),
        parse_mode="Markdown"
    )

@router.message(Command("new_dialog"))
async def new_dialog(message: Message, state: FSMContext):
    # Проверяем лимиты и получаем пользователя
    can_proceed, _ = await check_limits(message, "text")
    if not can_proceed:
        return

    # Получаем локализованные тексты для подтверждения
    user_language = await get_user_language(message.from_user.id)
    prompt_text = get_text("text_processing", "confirm_clear_dialog_prompt", user_language)
    yes_text = get_text("text_processing", "confirm_clear_dialog_yes", user_language)
    no_text = get_text("text_processing", "confirm_clear_dialog_no", user_language)

    # Создаем клавиатуру с кнопками подтверждения
    confirmation_kb = InlineKeyboardBuilder()
    confirmation_kb.add(InlineKeyboardButton(text=yes_text, callback_data="confirm_clear_yes"))
    confirmation_kb.add(InlineKeyboardButton(text=no_text, callback_data="confirm_clear_no"))
    confirmation_kb.adjust(2)  # Размещаем кнопки в одном ряду

    # Отправляем сообщение с запросом подтверждения
    await message.answer(prompt_text, reply_markup=confirmation_kb.as_markup())
    await state.clear()

@router.callback_query(F.data == "confirm_clear_yes")
async def process_confirm_clear_yes(callback_query: CallbackQuery):
    # Получаем пользователя из базы данных
    user = await get_user_by_tg_id(callback_query.from_user.id)
    user_language = await get_user_language(callback_query.from_user.id)

    if not user:
        error_message = get_text("errors", "user_not_found", user_language)
        await callback_query.message.edit_text(error_message, reply_markup=None)
        await callback_query.answer()
        return

    # Удаляем сообщения пользователя по ID из Supabase
    success = await delete_user_messages(user["id"])

    # Получаем текст результата
    if success:
        result_text = get_text("text_processing", "confirm_clear_dialog_success", user_language)
    else:
        result_text = get_text("errors", "dialog_clear_failed", user_language)

    # Редактируем сообщение с результатом
    await callback_query.message.edit_text(result_text, reply_markup=None)

    # Получаем текст для всплывающего уведомления
    if success:
        toast_text = get_text("text_processing", "confirm_clear_dialog_success_toast", user_language)
    else:
        toast_text = get_text("text_processing", "confirm_clear_dialog_failed_toast", user_language)

    # Отправляем всплывающее уведомление
    await callback_query.answer(text=toast_text, show_alert=False)

@router.callback_query(F.data == "confirm_clear_no")
async def process_confirm_clear_no(callback_query: CallbackQuery):
    # Получаем текст отмены
    user_language = await get_user_language(callback_query.from_user.id)
    cancel_text = get_text("text_processing", "confirm_clear_dialog_cancelled", user_language)

    # Редактируем сообщение с текстом отмены
    await callback_query.message.edit_text(cancel_text, reply_markup=None)

    # Получаем текст для всплывающего уведомления
    toast_text = get_text("text_processing", "confirm_clear_dialog_cancelled_toast", user_language)

    # Отправляем всплывающее уведомление
    await callback_query.answer(text=toast_text, show_alert=False)

@router.message(Generate.text)
async def generate_error(message: Message):
    user_language = await get_user_language(message.from_user.id)
    wait_message = get_text("text_processing", "wait_generation", user_language)
    await message.answer(wait_message)

# --- Изменена сигнатура: добавлен dispatcher ---
@router.message(F.text & ~F.text.startswith("/"))
async def generate(message: Message, state: FSMContext, bot: Bot, dispatcher: Dispatcher):
    # Проверяем лимиты и получаем пользователя
    can_proceed, user = await check_limits(message, "text")
    if not can_proceed:
        return

    user_id = user["id"]

    # Отправляем сообщение об обработке
    user_language = await get_user_language(message.from_user.id)
    processing_message = await message.answer(get_text("text_processing", "processing", user_language))

    # Показываем индикатор "печатает"
    await bot.send_chat_action(chat_id=message.chat.id, action="typing")
    await state.set_state(Generate.text)

    start_time = time.time()  # Запускаем таймер

    try:
        # --- Получаем mcp_manager из dispatcher ---
        mcp_manager_instance = dispatcher.get('mcp_manager')
        # ---------------------------------------

        # --- Функция уведомления об использовании инструментов ---
        async def notify_tool_usage(tool_names: List[str]):
            user_language = await get_user_language(message.from_user.id)
            tool_usage_message = get_text("text_processing", "using_mcp_tools", user_language)
            await processing_message.edit_text(tool_usage_message)
        # ---------------------------------------

        # --- Передаем все необходимые параметры в llm_response ---
        response = await llm_response(
            message.text,
            message.from_user.id,
            mcp_manager_instance,
            bot=message.bot,
            message=message,
            tool_usage_callback=notify_tool_usage
        )
        # -----------------------------------------

        end_time = time.time()  # Останавливаем таймер
        processing_time = end_time - start_time  # Вычисляем время выполнения

        if response is None:
            raise ValueError("Не удалось получить ответ от llm_response")

        # Проверяем, содержит ли ответ ошибку
        if isinstance(response, dict) and "error" in response:
            error_message = response["error"]
            await processing_message.edit_text(error_message, parse_mode="Markdown")
            return

        # Получаем информацию о модели пользователя
        user_model = await get_user_model(message.from_user.id)
        model_name = MODELS_CONFIG[user_model]['name']

        # Получаем основной текст ответа
        response_content = response.choices[0].message.content

        # Конвертируем Markdown в HTML
        html_response_content = convert_markdown_to_html(response_content)

        # Сохраняем сообщения в Supabase (сохраняем только основной контент без времени)
        # --- Сохраняем и запрос на вызов инструмента, и финальный ответ ---
        # Сохраняем исходный запрос пользователя
        await save_message("user", message.text, user_id)
        # Если был вызов инструмента, сохраняем запрос ассистента на вызов
        # и результаты вызова (они уже в response, но для БД лучше сохранить отдельно)
        if response.choices[0].message.tool_calls:
             assistant_tool_request_content = response.choices[0].message.content or "[Запрос на вызов инструмента]"
             await save_message("assistant", assistant_tool_request_content, user_id)
             # TODO: Возможно, стоит сохранять и результаты tool_calls в отдельное поле/таблицу
        # Сохраняем финальный ответ ассистента
        await save_message("assistant", response_content, user_id)
        # --------------------------------------------------------------------

        try:
            user_language = await get_user_language(message.from_user.id)
            processing_info_template = get_text("text_processing", "processing_info", user_language)
            formatted_response = processing_info_template.format(
                response_content=html_response_content,
                processing_time=processing_time,
                model_name=model_name
            )
            await processing_message.edit_text(formatted_response, parse_mode=ParseMode.HTML)
            await SubscriptionManager.increment_request_counter(message.from_user.id, "text")
        except TelegramBadRequest as e:
            logger.error(f"Ошибка отправки HTML сообщения: {e}")
            try:
                user_language = await get_user_language(message.from_user.id)
                processing_info_template_fallback = get_text("text_processing", "processing_info", user_language)
                processing_info_template_fallback = processing_info_template_fallback.replace("<i>", "").replace("</i>", "")
                fallback_response = processing_info_template_fallback.format(
                    response_content=response_content,
                    processing_time=processing_time,
                    model_name=model_name
                )
                await processing_message.edit_text(fallback_response, parse_mode=None)
                await SubscriptionManager.increment_request_counter(message.from_user.id, "text")
            except Exception as e2:
                logger.error(f"Ошибка отправки fallback текста: {e2}")
                await message.answer("Произошла ошибка при отправке ответа.")
        except Exception as e:
            await handle_error(e, message, "errors.unexpected_error", additional_info="Неожиданная ошибка при отправке ответа LLM")

    except ValueError as ve:
        await handle_error(ve, message, "text_processing.generation_error")
    except Exception as e:
        await handle_error(e, message, "errors.unexpected_error", additional_info="Неожиданная ошибка")

    finally:
        await state.clear()