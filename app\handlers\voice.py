import logging
from aiogram import Router, F, Dispatcher
from aiogram.types import Message
from app.voice_messages import handle_voice_message
from app.utils.error_handler import handle_error

router = Router()
logger = logging.getLogger(__name__)

@router.message(F.voice)
async def handle_voice(message: Message, dispatcher: Dispatcher):
    # Обработка голосового сообщения
    try:
        await handle_voice_message(message, message.from_user.id, dispatcher)
    except PermissionError as e:
        await handle_error(e, message, "errors.voice_processing_error", additional_info="Ошибка при обработке голосового сообщения")