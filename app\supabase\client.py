import os
from dotenv import load_dotenv
from supabase import create_client, Client
import logging

# Настройка логирования
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
load_dotenv()

class SupabaseClient:
    """
    Класс для работы с Supabase.
    Реализует паттерн Singleton для обеспечения единственного экземпляра клиента.
    """
    _instance = None
    _client = None
    _supabase_url = os.environ.get("SUPABASE_URL")
    _supabase_key = os.environ.get("SUPABASE_ANON_KEY")
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SupabaseClient, cls).__new__(cls)
            cls._instance._initialize_client()
        return cls._instance
    
    def _initialize_client(self):
        """
        Инициализирует клиент Supabase.
        """
        try:
            if not self._supabase_url or not self._supabase_key:
                raise ValueError("Не найдены переменные окружения SUPABASE_URL или SUPABASE_ANON_KEY")
            
            self._client = create_client(self._supabase_url, self._supabase_key)
            logger.info("Supabase клиент успешно инициализирован")
        except Exception as e:
            logger.error(f"Ошибка при инициализации Supabase клиента: {str(e)}")
            raise
    
    @property
    def client(self) -> Client:
        """
        Возвращает клиент Supabase.
        
        Returns:
            Client: Клиент Supabase
        """
        return self._client

# Создаем глобальный экземпляр клиента
supabase_client = SupabaseClient().client 