-- Обновляем роли пользователей
UPDATE users
SET role =
    CASE
        WHEN role = 'Default.txt' THEN 'default'
        WHEN role = 'Emily.txt' THEN 'emily'
        WHEN role = 'Lsaac.txt' THEN 'isaac' -- Обрати внимание на Lsaac.txt
        WHEN role = 'Tutor.txt' THEN 'tutor'
        -- Добавь сюда другие роли, если они были
        ELSE 'default' -- Устанавливаем 'default' для всех остальных или неизвестных ролей
    END
WHERE role IN ('Default.txt', 'Emily.txt', 'Lsaac.txt', 'Tutor.txt' /* Добавь другие старые имена файлов */);

-- Опционально: Проверяем, что не осталось старых имен файлов
-- SELECT tg_id, role FROM users WHERE role LIKE '%.txt';
