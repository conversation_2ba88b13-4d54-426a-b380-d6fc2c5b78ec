import sys
import os

# Добавляем корневую директорию проекта в sys.path
# Это нужно для корректного импорта модулей проекта, таких как config
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import asyncio
import argparse
import os
import time
import statistics
import random
import string
from openai import AsyncOpenAI

# Импорт настроек из конфигурации проекта
from config.models_config import get_model_config, DEFAULT_MODEL, get_api_key, BASE_API_URL

async def make_llm_request(client: AsyncOpenAI, model_name: str) -> tuple[bool, float, str | None]:
    """
    Отправляет один запрос к API LLM и измеряет время ответа.

    Args:
        client: Асинхронный клиент OpenAI.
        model_name: Название модели LLM для использования.

    Returns:
        Кортеж: (успех запроса, время ответа, сообщение об ошибке или None)
    """
    start_time = time.monotonic()
    try:
        # Генерируем случайный контент для запроса
        random_content = ''.join(random.choices(string.ascii_letters + string.digits, k=10))

        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": random_content},
        ]
        response = await client.chat.completions.create(
            model=model_name,
            messages=messages,
            max_tokens=50 # Ограничим количество токенов для более быстрого ответа
        )
        end_time = time.monotonic()
        return True, end_time - start_time, None
    except Exception as e:
        end_time = time.monotonic()
        return False, end_time - start_time, str(e)

async def run_load_test(concurrency: int, total_requests: int, base_url: str, api_key: str, model_name: str):
    """
    Выполняет нагрузочное тестирование API LLM.

    Args:
        concurrency: Количество одновременных запросов.
        total_requests: Общее количество запросов для выполнения.
        base_url: URL API LLM.
        api_key: API ключ для доступа к API.
    """
    client = AsyncOpenAI(
        base_url=base_url,
        api_key=api_key,
    )

    print(f"Начало нагрузочного тестирования: {total_requests} запросов с параллелизмом {concurrency}")
    print(f"API URL: {base_url}")

    request_tasks = []
    start_test_time = time.monotonic()

    # Создаем задачи для выполнения запросов
    for i in range(total_requests):
        task = asyncio.create_task(make_llm_request(client, model_name))
        request_tasks.append(task)

    results = await asyncio.gather(*request_tasks)

    end_test_time = time.monotonic()
    total_test_duration = end_test_time - start_test_time

    successful_requests = 0
    failed_requests = 0
    response_times = []
    errors = {}

    for success, duration, error_msg in results:
        if success:
            successful_requests += 1
            response_times.append(duration)
        else:
            failed_requests += 1
            error_type = error_msg.split(':')[0] if error_msg else "Unknown Error"
            errors[error_type] = errors.get(error_type, 0) + 1

    print("\n--- Результаты тестирования ---")
    print(f"Общее время выполнения: {total_test_duration:.2f} сек")
    print(f"Общее количество запросов: {total_requests}")
    print(f"Успешных запросов: {successful_requests}")
    print(f"Неуспешных запросов: {failed_requests}")

    if response_times:
        print(f"Среднее время ответа: {statistics.mean(response_times):.4f} сек")
        print(f"Медиана времени ответа: {statistics.median(response_times):.4f} сек")
        print(f"Минимальное время ответа: {min(response_times):.4f} сек")
        print(f"Максимальное время ответа: {max(response_times):.4f} сек")

    if failed_requests > 0:
        error_percentage = (failed_requests / total_requests) * 100
        print(f"Процент ошибок: {error_percentage:.2f}%")
        print("Детали ошибок:")
        for error_type, count in errors.items():
            print(f"  {error_type}: {count}")
    else:
        print("Процент ошибок: 0.00%")

def main():
    parser = argparse.ArgumentParser(description="Скрипт нагрузочного тестирования API LLM.")
    parser.add_argument(
        "--concurrency",
        type=int,
        default=10,
        help="Количество одновременных запросов (по умолчанию: 10)",
    )
    parser.add_argument(
        "--total-requests",
        type=int,
        default=100,
        help="Общее количество запросов для выполнения (по умолчанию: 100)",
    )
    parser.add_argument(
        "--base-url",
        type=str,
        default=None, # Убираем дефолтное значение из os.environ.get
        help=f"URL API LLM (по умолчанию: используется из конфига '{DEFAULT_MODEL}')",
    )
    parser.add_argument(
        "--api-key",
        type=str,
        default=None, # Убираем дефолтное значение из os.environ.get
        help=f"API ключ для доступа к API (по умолчанию: используется из конфига '{DEFAULT_MODEL}')",
    )
    parser.add_argument(
        "--model",
        type=str,
        default=None,
        help=f"Название модели LLM для использования (по умолчанию: используется из конфига '{DEFAULT_MODEL}', запасной вариант: gpt-4o)",
    )

    args = parser.parse_args()

    # Логика получения настроек: сначала аргументы/переменные окружения, затем конфиг
    base_url = args.base_url if args.base_url is not None else os.environ.get("LLM_BASE_URL")
    api_key = args.api_key if args.api_key is not None else os.environ.get("LLM_API_KEY")

    # Логика получения настроек API: сначала аргументы/переменные окружения, затем конфиг
    base_url = args.base_url if args.base_url is not None else os.environ.get("LLM_BASE_URL")
    api_key = args.api_key if args.api_key is not None else os.environ.get("LLM_API_KEY")

    # Логика определения модели: сначала аргумент --model, затем конфиг, затем gpt-4o
    model_name = args.model
    if model_name is None:
        # Если аргумент --model не передан, пытаемся взять из конфига
        config_from_default_model = get_model_config(DEFAULT_MODEL)
        model_name = config_from_default_model.get("model")
        if model_name is None:
            # Если и в конфиге нет, используем gpt-4o как запасной вариант
            model_name = "gpt-4o"

    # Если base_url или api_key не были найдены через аргументы или переменные окружения, берем из конфига
    if base_url is None or api_key is None:
        config_from_provided_model = get_model_config(model_name) # Попробуем взять конфиг для выбранной модели
        if base_url is None:
             base_url = config_from_provided_model.get("base_url")
        if api_key is None:
             # Используем get_api_key() напрямую, т.к. в конфиге может быть None.
             # get_api_key() сам проверяет переменные окружения и конфиг
             api_key = get_api_key()

    if not base_url or not api_key:
        print("Ошибка: Не удалось получить URL API LLM или API ключ.")
        print("Пожалуйста, укажите их через аргументы --base-url и --api-key,")
        print(f"переменные окружения LLM_BASE_URL и LLM_API_KEY, или настройте")
        print(f"значения 'base_url' и 'api_key' для используемой модели ('{model_name}' или '{DEFAULT_MODEL}') в 'config/models_config.py'.")
        return

    asyncio.run(run_load_test(args.concurrency, args.total_requests, base_url, api_key, model_name))

if __name__ == "__main__":
    main()