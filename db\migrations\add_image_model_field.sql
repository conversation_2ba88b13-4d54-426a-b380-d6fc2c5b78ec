-- =============================================
-- МИГРАЦИЯ: ДОБАВЛЕНИЕ ПОЛЯ image_model
-- =============================================
-- Этот скрипт добавляет поле image_model в таблицу users для хранения
-- модели генерации изображений, используемой пользователем.
--
-- Дата создания: 2025-01-27
-- Версия: 1.0
--
-- Инструкция по использованию:
-- 1. Войдите в панель управления Supabase (https://app.supabase.io)
-- 2. Выберите ваш проект
-- 3. Перейдите в раздел "SQL Editor"
-- 4. Создайте новый запрос, вставьте содержимое этого файла
-- 5. Выполните запрос
--
-- ВАЖНО: Этот скрипт безопасен для выполнения на существующей базе данных.
-- Он добавляет новое поле без изменения существующих данных.

-- Установка часового пояса для текущей сессии
SET timezone TO 'Europe/Moscow';

-- =============================================
-- ЧАСТЬ 1: ДОБАВЛЕНИЕ НОВОГО ПОЛЯ
-- =============================================

-- Добавляем поле image_model в таблицу users
ALTER TABLE public.users
ADD COLUMN image_model VARCHAR NOT NULL DEFAULT 'flux';

-- =============================================
-- ЧАСТЬ 2: ДОБАВЛЕНИЕ КОММЕНТАРИЯ К ПОЛЮ
-- =============================================

-- Добавляем комментарий к новому полю
COMMENT ON COLUMN public.users.image_model IS 'Модель для генерации изображений, используемая пользователем (flux, gptimage)';

-- =============================================
-- ЧАСТЬ 3: СОЗДАНИЕ ИНДЕКСА (ОПЦИОНАЛЬНО)
-- =============================================

-- Создаем индекс для оптимизации запросов по image_model (если потребуется)
-- CREATE INDEX idx_users_image_model ON public.users(image_model);

-- =============================================
-- ЧАСТЬ 4: ПРОВЕРКА РЕЗУЛЬТАТА
-- =============================================

-- Проверяем, что поле было успешно добавлено
-- Раскомментируйте следующие строки для проверки:

-- SELECT column_name, data_type, is_nullable, column_default
-- FROM information_schema.columns
-- WHERE table_name = 'users' AND column_name = 'image_model';

-- Проверяем, что у всех существующих пользователей установлено значение по умолчанию
-- SELECT COUNT(*) as total_users,
--        COUNT(CASE WHEN image_model = 'flux' THEN 1 END) as users_with_default_model
-- FROM public.users;

-- =============================================
-- ПРИМЕЧАНИЯ
-- =============================================
-- 1. Поле image_model добавлено с ограничением NOT NULL и значением по умолчанию 'flux'
-- 2. Все существующие пользователи автоматически получат модель 'flux' по умолчанию
-- 3. Новые пользователи также будут создаваться с моделью 'flux' по умолчанию
-- 4. Поле совместимо с существующей конфигурацией IMAGE_MODELS_CONFIG
-- 5. Триггер update_updated_at_column автоматически применится к новому полю
-- 6. Политики RLS (Row Level Security) автоматически распространятся на новое поле
-- =============================================
