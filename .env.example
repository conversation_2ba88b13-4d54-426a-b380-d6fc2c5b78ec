# Токен для доступа к Telegram Bot API
TG_TOKEN = "ваш_токен_telegram_bot"  # Токен Telegram Bot API

# Администраторы бота
ADMINS = "id_администратора"  # ID администратора бота

# API ключ моделей
LITELLM_API_KEY = "ваш_ключ"


# Установите максимальное количество токенов
MAX_TOKENS = 4096  # Максимальное количество токенов

# Максимальное количество сообщений, сохраняемых в базе данных для каждого пользователя
MAX_MESSAGES = 30  # Максимальное количество сохраняемых сообщений

# Максимальное количество изображений, используемых в функции компьютерного зрения
MAX_VISION_IMAGES = 3  # Максимальное количество изображений для vision

# Параметры голосового синтеза (openai.fm API)
TTS_VOICE="nova"
TTS_VIBE_PROMPT="Voice: Clear, authoritative, and composed, projecting confidence and professionalism. Tone: Neutral and informative, maintaining a balance between formality and approachability. Punctuation: Structured with commas and pauses for clarity, ensuring information is digestible and well-paced. Delivery: Steady and measured, with slight emphasis on key figures and deadlines to highlight critical points."

# Параметры базы данных Supabase
SUPABASE_URL = "https://your-supabase-url.com"
SUPABASE_ANON_KEY = "your-supabase-anon-key"

# Параметры кеширования
USER_CACHE_TTL = 300  # Время жизни кеша пользователей в секундах (5 минут)


TAVILY_API_KEY = "your-api-key-here"

# ========================================
# Настройки Pollinations.AI
# ========================================
# API токен для backend приложений (Discord боты, AI чатботы)
# URL: https://text.pollinations.ai/openai?token=YOUR_TOKEN
# Header: Authorization: Bearer YOUR_TOKEN
POLLINATIONS_API_TOKEN = "your-pollinations-api-token"

# Referrer/Domain для frontend приложений
# Автоматическая проверка домена для приоритетного доступа
# Если ваш сайт в списке доверенных - токен не нужен
POLLINATIONS_REFERRER = "your-domain.com"
