# app/localization/en/edit_image.py
TEXTS = {
    "no_context": "I can't find an image to edit. Please send a picture first or generate a new one.",
    "processing": "🎨 Making changes to the image... This might take a few seconds.",
    "upload_error": "An error occurred while uploading your image for editing. Please try again.",
    "api_error": "Failed to edit the image. The editing service might be temporarily unavailable.",
    "general_error": "An unexpected error occurred during editing. Please inform the developer.",
    "success": "Image was successfully edited and sent to the user."
}
