"""
Базовый интерфейс (протокол) для всех инструментов AI.

Этот модуль определяет контракт, которому должны следовать все инструменты
в системе Tool Calling. Использует typing.Protocol для обеспечения
статической типизации и четкого API.
"""

from typing import Protocol, Dict, Any
from aiogram import Bot


class Tool(Protocol):
    """
    Протокол для всех AI инструментов.

    Определяет обязательные атрибуты и методы, которые должен реализовать
    каждый инструмент для корректной работы с OpenAI API и системой регистрации.
    """

    # Обязательные атрибуты
    name: str
    """Уникальное имя инструмента для идентификации в OpenAI API"""

    description: str
    """Описание инструмента для LLM - что он делает и когда использовать"""

    schema: Dict[str, Any]
    """
    JSON-схема параметров инструмента в формате OpenAI Function Calling.

    Пример:
    {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "Поисковый запрос"
            }
        },
        "required": ["query"]
    }
    """

    async def execute(self, bot: Bot, chat_id: int, **kwargs) -> str:
        """
        Выполняет инструмент с переданными аргументами.

        Args:
            bot: Экземпляр Telegram бота для отправки сообщений
            chat_id: ID чата для взаимодействия с пользователем
            **kwargs: Аргументы, переданные от LLM согласно schema

        Returns:
            str: Результат выполнения инструмента в текстовом формате

        Raises:
            Exception: При ошибках выполнения инструмента
        """
        ...