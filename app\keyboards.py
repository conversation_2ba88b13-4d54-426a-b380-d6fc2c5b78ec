from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from config.models_config import MODELS_CONFIG
from config.subscription_config import SUBSCRIPTION_PLANS
from aiogram.utils.keyboard import InlineKeyboardBuilder
from app.localization import get_text


def get_back_button(language: str, callback_data: str) -> InlineKeyboardButton:
    """Создает локализованную кнопку 'Назад'"""
    back_text = get_text("common", "buttons", language).get("back", "◀️ Back")
    return InlineKeyboardButton(text=back_text, callback_data=callback_data)


def get_profile_button(language: str, callback_data: str = "back_to_profile") -> InlineKeyboardButton:
    """Создает локализованную кнопку 'Профиль'"""
    profile_text = get_text("common", "buttons", language).get("profile", "👤 Profile") # Используем тот же раздел "common", "buttons"
    return InlineKeyboardButton(text=profile_text, callback_data=callback_data)


def get_localized_role_name(role_id: str, language: str) -> str:
    """Получает локализованное название роли"""
    roles_texts = get_text("common", "roles", language)
    return roles_texts.get(role_id, role_id.capitalize())


def get_subscription_plans_keyboard(language: str) -> InlineKeyboardMarkup:
    """Создает локализованную клавиатуру для тарифных планов"""
    builder = InlineKeyboardBuilder()

    # Добавляем кнопки для покупки планов
    for plan_id, plan in SUBSCRIPTION_PLANS.items():
        if plan_id != "free":  # Исключаем бесплатный тариф
            if language == "en":
                button_text = f"Buy {plan['name']} ({plan['price']} Stars ⭐️)"
            else:
                button_text = f"Купить {plan['name']} ({plan['price']} Stars ⭐️)"

            builder.add(InlineKeyboardButton(
                text=button_text,
                callback_data=f"buy_plan_{plan_id}"
            ))

    # Добавляем кнопку оферты
    if language == "en":
        offer_text = "📋 Terms of Service"
    else:
        offer_text = "📋 Оферта"

    builder.add(InlineKeyboardButton(
        text=offer_text,
        url="https://telegra.ph/Publichnyj-dogovor---oferta-GPT-Project-01-08"
    ))

    # Добавляем локализованную кнопку "Назад"
    builder.add(get_back_button(language, "back_to_profile"))

    # Настраиваем компоновку
    builder.adjust(1)

    return builder.as_markup()


# Меню профиля пользователя inline-клавиатуры
user_profile = InlineKeyboardMarkup(
    inline_keyboard=[
        [InlineKeyboardButton(text="Настройки", callback_data="settings")],
    ]
)

# Функция для генерации клавиатуры настроек профиля с локализованным текстом
def get_profile_settings_keyboard(language: str) -> InlineKeyboardMarkup:
    """Генерирует клавиатуру настроек профиля с локализованным текстом."""
    builder = InlineKeyboardBuilder()
    # Получаем тексты кнопок для нужного языка
    buttons_texts = get_text("profile", "buttons", language)

    builder.row(
        InlineKeyboardButton(
            text=buttons_texts.get("change_model", "🧠 Change Model"),  # Fallback на англ.
            callback_data="change_model"
        )
    )
    builder.row(
        InlineKeyboardButton(
            text=buttons_texts.get("change_role", "🤠 Change Role"),
            callback_data="change_role"
        )
    )
    builder.row(
        InlineKeyboardButton(
            text=buttons_texts.get("image_settings", "🎨 Image Settings"),
            callback_data="image_settings"
        )
    )
    builder.row(
         InlineKeyboardButton(
            text=buttons_texts.get("subscription", "💎 Subscription"),
            callback_data="show_subscription_plans"
        )
    )
    builder.row(
        InlineKeyboardButton(
            text=buttons_texts.get("change_language", "🌐 Change Language"),
            callback_data="change_language"
        )
    )
    builder.row(
         InlineKeyboardButton(
            text=buttons_texts.get("privacy", "🔒 Privacy"),
            callback_data="show_privacy"
        )
    )
    return builder.as_markup()







# Добавляем константу с доступными размерами
IMAGE_SIZES = {
    "1:1 (Квадрат)": "1:1",
    "16:9 (Горизонтальный)": "16:9",
    "9:16 (Вертикальный)": "9:16",
    "21:9 (Широкий)": "21:9",
    "9:21 (Высокий)": "9:21",
    "1:2 (Высокий)": "1:2",
    "2:1 (Широкий)": "2:1"
}

# Создаем клавиатуру для выбора размера
image_size_selection = InlineKeyboardMarkup(
    inline_keyboard=[
        [InlineKeyboardButton(text=name, callback_data=f"size_{size}")]
        for name, size in IMAGE_SIZES.items()
    ]
)

# Клавиатура для выбора языка
language_kb = InlineKeyboardMarkup(
    inline_keyboard=[
        [InlineKeyboardButton(text="🇷🇺 Русский", callback_data="language:ru")],
        [InlineKeyboardButton(text="🇬🇧 English", callback_data="language:en")]
    ]
)

# Клавиатура для выбора языка при первом запуске
language_start_kb = InlineKeyboardMarkup(
    inline_keyboard=[
        [InlineKeyboardButton(text="🇷🇺 Русский", callback_data="start_language:ru")],
        [InlineKeyboardButton(text="🇬🇧 English", callback_data="start_language:en")]
    ]
)