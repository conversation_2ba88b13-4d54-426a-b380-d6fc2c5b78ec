import logging
from aiogram import Router
from aiogram.types import Message
from aiogram.filters import Command
from app.localization import get_text
from app.supabase.users import get_user_referral_code, get_user_referral_count, get_user_language

router = Router()
logger = logging.getLogger(__name__)

@router.message(Command("referral"))
async def referral_system(message: Message):
    referral_code = await get_user_referral_code(message.from_user.id)
    referral_count = await get_user_referral_count(message.from_user.id)
    bot_username = (await message.bot.get_me()).username

    referral_link = f"https://t.me/{bot_username}?start={referral_code}"

    user_language = await get_user_language(message.from_user.id)
    await message.answer(
        get_text("referral", "referral_info", user_language).format(
            referral_code=referral_code,
            referral_count=referral_count,
            referral_link=referral_link
        ),
        parse_mode="Markdown"
    )