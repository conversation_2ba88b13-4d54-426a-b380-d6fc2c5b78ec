# Скрипты миграции и инициализации базы данных

В этой директории содержатся SQL-скрипты для инициализации и миграции базы данных Supabase.

## Структура директории

- `init_supabase_schema.sql` - основной скрипт инициализации базы данных Supabase
- Другие скрипты миграции (будут добавляться по мере необходимости)

## Инициализация базы данных

Для инициализации новой базы данных Supabase выполните следующие шаги:

1. Войдите в панель управления Supabase (https://app.supabase.io)
2. Выберите ваш проект
3. Перейдите в раздел "SQL Editor"
4. Создайте новый запрос, вставьте содержимое файла `init_supabase_schema.sql`
5. Выполните запрос

## Важные примечания

- **Выполняйте скрипт инициализации только на новой базе данных!** Выполнение на существующей базе может привести к потере данных.
- Скрипт создает все необходимые таблицы, индексы, триггеры и политики безопасности для работы Telegram-бота.
- По умолчанию настроены политики безопасности, разрешающие полный доступ анонимному ключу для упрощения разработки. В продакшн-версии рекомендуется настроить более строгие политики.

## Миграция данных из SQLite

Если вам необходимо перенести данные из существующей базы данных SQLite в Supabase, рекомендуется использовать отдельный скрипт миграции, который будет создан в рамках следующего этапа разработки. 