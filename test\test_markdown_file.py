# test_markdown_file.py
import asyncio
import logging
import os
import sys
import argparse # Добавляем argparse для обработки аргументов
import aiofiles # Для асинхронного чтения файла
from dotenv import load_dotenv
from aiogram import Bot
from aiogram.enums import ParseMode
from aiogram.exceptions import TelegramBadRequest
from colorama import Fore, Style, init as colorama_init

# --- Добавляем путь к проекту ---
current_dir = os.path.dirname(os.path.abspath(__file__))
# Определяем корень проекта (на один уровень выше, если скрипт в папке test)
project_root = os.path.dirname(current_dir) if os.path.basename(current_dir).lower() == 'test' else current_dir

if project_root not in sys.path:
    sys.path.insert(0, project_root)
    logging.info(f"Добавлен путь к проекту в sys.path: {project_root}")

# --- Импортируем нашу функцию конвертации ---
try:
    from app.utils.formatting import convert_markdown_to_html
except ImportError as e:
    logging.critical(f"Не удалось импортировать convert_markdown_to_html из app.utils.formatting: {e}")
    logging.critical("Убедитесь, что структура проекта верна и скрипт запускается корректно.")
    exit()

# --- Настройка логирования ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# --- Инициализация Colorama ---
colorama_init(autoreset=True)

# --- Загрузка переменных окружения ---
load_dotenv()
BOT_TOKEN = os.getenv("TG_TOKEN")
TEST_CHAT_ID = os.getenv("TEST_CHAT_ID")

if not BOT_TOKEN:
    logger.critical("Не найден токен бота в переменной окружения TG_TOKEN.")
    exit()
if not TEST_CHAT_ID:
    logger.critical("Не найден ID чата в переменной окружения TEST_CHAT_ID.")
    logger.critical("Добавьте TEST_CHAT_ID=<ваш_chat_id> в файл .env")
    exit()
else:
    try:
        TEST_CHAT_ID = int(TEST_CHAT_ID)
    except ValueError:
        logger.critical("TEST_CHAT_ID должен быть числовым идентификатором чата.")
        exit()

# --- Парсер аргументов командной строки ---
parser = argparse.ArgumentParser(description="Тестирует конвертацию Markdown файла в HTML и отправляет результат в Telegram.")
parser.add_argument("filepath", help="Путь к Markdown файлу (.md)")

# --- Основная асинхронная функция ---
async def main():
    args = parser.parse_args()
    filepath = args.filepath

    if not os.path.exists(filepath):
        logger.error(f"Файл не найден: {filepath}")
        print(f"{Fore.RED}Ошибка: Файл не найден по указанному пути: {filepath}{Style.RESET_ALL}")
        return

    if not filepath.lower().endswith(".md"):
         logger.warning(f"Файл '{filepath}' не имеет расширения .md. Обработка будет продолжена.")
         print(f"{Fore.YELLOW}Предупреждение: Файл '{filepath}' не имеет расширения .md.{Style.RESET_ALL}")

    bot = Bot(token=BOT_TOKEN)
    logger.info(f"Тестирование файла: {filepath}")
    logger.info(f"Результаты будут отправляться в чат ID: {TEST_CHAT_ID}")

    try:
        # --- Чтение содержимого файла ---
        logger.info(f"Чтение файла {filepath}...")
        async with aiofiles.open(filepath, mode='r', encoding='utf-8') as f:
            md_text = await f.read()
        logger.info(f"Файл успешно прочитан ({len(md_text)} символов).")
        print(f"{Fore.YELLOW}--- Исходный Markdown ---{Style.RESET_ALL}")
        print(md_text)
        print(f"{Fore.YELLOW}------------------------{Style.RESET_ALL}")


        # --- Конвертация Markdown в HTML ---
        logger.info("Конвертация Markdown -> HTML...")
        try:
            converted_html = convert_markdown_to_html(md_text)
            print(f"{Fore.YELLOW}--- Результат конвертации (HTML) ---{Style.RESET_ALL}")
            print(converted_html)
            print(f"{Fore.YELLOW}------------------------------------{Style.RESET_ALL}")
        except Exception as e:
            logger.error(f"Ошибка при конвертации: {e}", exc_info=True)
            print(f"{Fore.RED}Ошибка конвертации: {e}{Style.RESET_ALL}")
            return # Выход, если конвертация не удалась

        # --- Отправка результата в Telegram ---
        logger.info(f"Отправка HTML в чат {TEST_CHAT_ID}...")
        try:
            await bot.send_message(
                chat_id=TEST_CHAT_ID,
                text=converted_html,
                parse_mode=ParseMode.HTML,
                disable_web_page_preview=True
            )
            logger.info("HTML успешно отправлен в Telegram.")
            print(f"{Fore.GREEN}✅ HTML успешно отправлен в Telegram.{Style.RESET_ALL}")
        except TelegramBadRequest as e:
            logger.error(f"Ошибка Telegram API при отправке HTML: {e.message}")
            print(f"{Fore.RED}❌ Ошибка Telegram API: {e.message}{Style.RESET_ALL}")
        except Exception as e:
            logger.error(f"Непредвиденная ошибка при отправке HTML: {e}", exc_info=True)
            print(f"{Fore.RED}❌ Непредвиденная ошибка отправки: {e}{Style.RESET_ALL}")

    except FileNotFoundError:
        logger.error(f"Файл не найден: {filepath}") # Повторная проверка на всякий случай
        print(f"{Fore.RED}Ошибка: Файл не найден: {filepath}{Style.RESET_ALL}")
    except IOError as e:
        logger.error(f"Ошибка чтения файла {filepath}: {e}", exc_info=True)
        print(f"{Fore.RED}Ошибка чтения файла: {e}{Style.RESET_ALL}")
    except Exception as e:
        logger.error(f"Произошла общая ошибка: {e}", exc_info=True)
        print(f"{Fore.RED}Произошла ошибка: {e}{Style.RESET_ALL}")
    finally:
        # Закрываем сессию бота перед выходом
        await bot.session.close()
        logger.info("Сессия бота закрыта.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        logger.critical(f"Критическая ошибка при запуске: {e}", exc_info=True)