# app/services/image_generation/providers/openai.py
import asyncio
import base64
import logging
import os
from typing import Any, Dict, Optional

from openai import AsyncOpenAI
from app.services.image_generation.base import ImageGenerator
from config.image_generation_config import get_image_dimensions

logger = logging.getLogger(__name__)


class OpenaiGenerator(ImageGenerator):
    """
    Провайдер для генерации изображений через OpenAI-совместимый API.
    Поддерживает различные провайдеры (Together AI, локальные серверы и др.)
    через указание base_url в конфигурации.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Инициализирует провайдер с его конфигурацией.
        """
        self.config = config
        self.base_url = self.config.get("base_url")
        self.model = self.config.get("model", "black-forest-labs/FLUX.1-dev")
        self.timeout = self.config.get("timeout", 60)
        self.max_retries = self.config.get("max_retries", 3)
        self.retry_delay = self.config.get("retry_delay", 10)
        
        # Получаем API ключ из переменных окружения
        api_key_env = self.config.get("api_key_env", "OPENAI_IMAGE_KEY")
        self.api_key = os.getenv(api_key_env)
        
        if not self.api_key:
            raise ValueError(f"API ключ не найден в переменной окружения '{api_key_env}'")
        
        if not self.base_url:
            raise ValueError("В конфигурации для OpenaiGenerator отсутствует 'base_url'")
        
        # Инициализируем клиент OpenAI
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
            timeout=self.timeout
        )
        
        logger.info(f"Инициализирован OpenaiGenerator с base_url: {self.base_url}, model: {self.model}")

    async def generate(
        self, prompt: str, size_key: str, retry_count: int = 0
    ) -> Optional[bytes]:
        """
        Генерирует изображение, реализуя "контракт" ImageGenerator.
        """
        try:
            # Получаем размеры изображения
            width, height = get_image_dimensions(size_key)
            
            # Формируем размер в формате "widthxheight" для OpenAI API
            size_str = f"{width}x{height}"
            
            logger.info(f"Генерация изображения: prompt='{prompt[:50]}...', size={size_str}, model={self.model}")
            
            # Вызываем API для генерации изображения
            response = await self.client.images.generate(
                model=self.model,
                prompt=prompt,
                size=size_str,
                response_format="b64_json",
                n=1
            )
            
            # Извлекаем base64 данные изображения
            if response.data and len(response.data) > 0:
                image_base64 = response.data[0].b64_json
                if image_base64:
                    # Декодируем base64 в байты
                    image_bytes = base64.b64decode(image_base64)
                    logger.info(f"Изображение успешно сгенерировано, размер: {len(image_bytes)} байт")
                    return image_bytes
                else:
                    logger.error("Получен пустой base64 ответ от API")
                    return None
            else:
                logger.error("API вернул пустой ответ")
                return None
                
        except Exception as e:
            logger.error(f"Ошибка при генерации изображения: {e}")
            
            # Повторяем попытку при ошибке
            if retry_count < self.max_retries:
                logger.info(f"Повторная попытка {retry_count + 1}/{self.max_retries} через {self.retry_delay} сек")
                await asyncio.sleep(self.retry_delay)
                return await self.generate(prompt, size_key, retry_count + 1)
            
            logger.error(f"Исчерпаны все попытки генерации изображения")
            return None

    def _sanitize_prompt(self, prompt: str) -> str:
        """
        Очищает и подготавливает промпт для API.
        """
        if not prompt or not prompt.strip():
            return ""
        
        # Ограничиваем длину промпта
        max_length = self.config.get("max_prompt_length", 1000)
        if len(prompt) > max_length:
            prompt = prompt[:max_length].rsplit(' ', 1)[0]  # Обрезаем по словам
        
        return prompt.strip()
