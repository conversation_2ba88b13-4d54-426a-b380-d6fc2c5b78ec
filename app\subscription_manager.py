from datetime import datetime, timedelta, timezone
from config.subscription_config import SUBSCRIPTION_PLANS, DEFAULT_PLAN
from app.supabase.users import get_user_by_tg_id, update_user_limits, update_user_subscription, get_user_language
from app.localization import get_text

class SubscriptionManager:
    @staticmethod
    def get_moscow_time():
        # Создаем часовой пояс UTC+3 (Moscow)
        moscow_tz = timezone(timedelta(hours=3))
        return datetime.now(moscow_tz)

    @staticmethod
    async def check_and_reset_limits(user_id: int) -> None:
        # Получаем пользователя из Supabase
        user = await get_user_by_tg_id(user_id)
        if user:
            # Получаем текущее время в МСК
            current_moscow_time = SubscriptionManager.get_moscow_time()

            # Конвертируем время последнего запроса в МСК
            last_request_str = user["last_request_date"]
            last_request_date = datetime.fromisoformat(last_request_str.replace('Z', '+00:00'))
            last_request_moscow = last_request_date.astimezone(timezone(timedelta(hours=3)))

            # Сравниваем даты в МСК
            if last_request_moscow.date() < current_moscow_time.date():
                # Обновляем лимиты в Supabase
                await update_user_limits(
                    user["id"],
                    0,  # text_requests_today
                    0,  # image_requests_today
                    current_moscow_time.isoformat()  # last_request_date
                )

    @staticmethod
    async def check_subscription_expiration(user: dict) -> bool:
        """
        Проверяет, истекла ли подписка пользователя.

        Args:
            user: Данные пользователя из базы данных

        Returns:
            bool: True, если подписка истекла и была сброшена, иначе False
        """
        # Проверяем только для платных тарифов
        if user["subscription_plan"] != DEFAULT_PLAN and user["subscription_end_date"]:
            current_time = SubscriptionManager.get_moscow_time()

            # Преобразуем строку даты в объект datetime
            subscription_end_date = datetime.fromisoformat(user["subscription_end_date"].replace('Z', '+00:00'))

            # Убедимся, что subscription_end_date имеет временную зону
            if subscription_end_date.tzinfo is None:
                subscription_end_date = subscription_end_date.replace(
                    tzinfo=timezone(timedelta(hours=3))
                )

            # Если подписка истекла, сбрасываем на бесплатный тариф
            if current_time > subscription_end_date:
                # Запоминаем название плана до сброса для уведомления
                expired_plan_name = SUBSCRIPTION_PLANS[user["subscription_plan"]]["name"]

                # Сбрасываем на бесплатный тариф
                await update_user_subscription(
                    user["id"],
                    DEFAULT_PLAN,
                    None  # Убираем дату окончания
                )

                # Сохраняем информацию о том, что подписка истекла
                user["subscription_expired"] = {
                    "plan_name": expired_plan_name
                }

                return True

        return False

    @staticmethod
    async def can_make_request(user_id: int, request_type: str) -> tuple[bool, str]:
        await SubscriptionManager.check_and_reset_limits(user_id)

        # Получаем пользователя из Supabase
        user = await get_user_by_tg_id(user_id)
        if not user:
            return False, "🤔 Кажется, я вас не узнаю!\nДавайте начнем сначала — напишите /start"

        # Проверяем, не истекла ли подписка
        subscription_expired = await SubscriptionManager.check_subscription_expiration(user)
        if subscription_expired:
            # Если подписка истекла, получаем обновленные данные пользователя
            user = await get_user_by_tg_id(user_id)

            # Формируем сообщение об истечении подписки
            expired_plan_name = user.get("subscription_expired", {}).get("plan_name", "платная")
            user_language = await get_user_language(user_id)
            expiration_message = get_text("subscription", "expired", user_language)
            expiration_message = expiration_message.format(plan_name=expired_plan_name)

            # Возвращаем сообщение об истечении подписки и разрешаем запрос
            # (пользователь будет использовать бесплатный тариф)
            return True, expiration_message

        plan = SUBSCRIPTION_PLANS[user["subscription_plan"]]
        hours, minutes = SubscriptionManager.get_time_until_reset()

        # Формируем сообщение с оставшимися запросами
        text_remaining = max(0, plan["text_requests_per_day"] - user["text_requests_today"])
        image_remaining = max(0, plan["image_requests_per_day"] - user["image_requests_today"])

        user_language = await get_user_language(user_id)
        limit_message = get_text("errors", "limit_exceeded", user_language)
        limit_message = limit_message.format(
            text_remaining=text_remaining,
            image_remaining=image_remaining,
            hours_until_reset=hours
        )

        if request_type == "text":
            # Проверяем достижение лимита текстовых запросов
            is_limit_reached = user["text_requests_today"] >= plan["text_requests_per_day"]
            return not is_limit_reached, limit_message if is_limit_reached else ""
        elif request_type == "image":
            # Проверяем достижение лимита запросов изображений
            is_limit_reached = user["image_requests_today"] >= plan["image_requests_per_day"]
            return not is_limit_reached, limit_message if is_limit_reached else ""

        return False, "Неверный тип запроса"

    @staticmethod
    async def increment_request_counter(user_id: int, request_type: str) -> None:
        # Получаем пользователя из Supabase
        user = await get_user_by_tg_id(user_id)
        if user:
            # Получаем текущее время в МСК
            current_moscow_time = SubscriptionManager.get_moscow_time()

            # Увеличиваем соответствующий счетчик
            text_requests = user["text_requests_today"]
            image_requests = user["image_requests_today"]

            if request_type == "text":
                text_requests += 1
            elif request_type == "image":
                image_requests += 1

            # Обновляем данные в Supabase
            await update_user_limits(
                user["id"],
                text_requests,
                image_requests,
                current_moscow_time.isoformat()
            )

    @staticmethod
    def get_time_until_reset():
        current_time = SubscriptionManager.get_moscow_time()
        next_reset = current_time.replace(hour=0, minute=0, second=0, microsecond=0)

        if current_time.time() >= next_reset.time():
            next_reset = next_reset + timedelta(days=1)

        time_left = next_reset - current_time
        hours = int(time_left.total_seconds() // 3600)
        minutes = int((time_left.total_seconds() % 3600) // 60)

        return hours, minutes