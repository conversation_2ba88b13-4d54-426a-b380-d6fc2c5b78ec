"""
Модуль для централизованной обработки ошибок.
Содержит функции для логирования ошибок и отправки сообщений пользователям.

Этот модуль предоставляет единый интерфейс для обработки ошибок во всем приложении.
В будущем планируется расширить функциональность для:
- Отправки уведомлений администраторам о критических ошибках
- Сбора статистики по типам ошибок
- Автоматического восстановления после определенных типов ошибок
- Интеграции с системами мониторинга
"""
import logging
import asyncio
from typing import Optional, Callable
from aiogram.types import Message
from app.localization import get_text
from app.supabase.users import get_user_language

logger = logging.getLogger(__name__)

async def handle_error(
    exception: Exception,
    message: Message,
    error_key: str,
    user_id: Optional[int] = None,
    log_level: int = logging.ERROR,
    cleanup_func: Optional[Callable] = None,
    additional_info: str = ""
) -> None:
    """
    Обрабатывает исключение, логирует его и отправляет сообщение пользователю.

    Args:
        exception: Исключение, которое нужно обработать
        message: Объект сообщения Telegram
        error_key: Ключ для локализованного сообщения об ошибке (формат: "категория.ключ" или просто "ключ")
        user_id: ID пользователя (если отличается от message.from_user.id).
               Полезно при обработке callback-запросов или административных функций.
        log_level: Уровень логирования. Можно использовать logging.WARNING, logging.ERROR или logging.CRITICAL
               в зависимости от важности ошибки.
        cleanup_func: Функция для выполнения дополнительных действий (например, удаление сообщений или временных файлов).
                    Может быть как синхронной, так и асинхронной функцией.
        additional_info: Дополнительная информация для логирования. Добавляется в логи, но не показывается пользователю.
                      Полезно для добавления технических деталей об ошибке.

    Примеры использования:
    ```python
    # Простой вызов
    await handle_error(e, message, "errors.database_error")

    # С дополнительной информацией
    await handle_error(e, message, "errors.processing_error",
                      additional_info="Ошибка при обработке файла {file_path}")

    # С функцией очистки
    await handle_error(e, message, "errors.file_error",
                      cleanup_func=lambda: os.remove(temp_file) if os.path.exists(temp_file) else None)
    ```
    """
    # Определяем категорию и ключ ошибки
    if "." in error_key:
        category, key = error_key.split(".", 1)
    else:
        category, key = "errors", error_key

    # Логируем ошибку
    log_message = f"Ошибка {exception.__class__.__name__}: {str(exception)}"
    if additional_info:
        log_message += f" | {additional_info}"

    logger.log(log_level, log_message)

    # Получаем ID пользователя
    user_id = user_id or message.from_user.id

    # Получаем локализованное сообщение об ошибке
    try:
        user_language = await get_user_language(user_id)
        error_message = get_text(category, key, user_language)
    except Exception as e:
        logger.error(f"Не удалось получить текст ошибки: {e}")
        error_message = f"Произошла ошибка: {exception.__class__.__name__}"

    # Отправляем сообщение пользователю
    try:
        await message.answer(error_message)
    except Exception as e:
        logger.error(f"Не удалось отправить сообщение об ошибке: {e}")

    # Выполняем дополнительные действия, если они указаны
    if cleanup_func:
        try:
            if asyncio.iscoroutinefunction(cleanup_func):
                await cleanup_func()
            else:
                cleanup_func()
        except Exception as e:
            logger.error(f"Ошибка при выполнении cleanup_func: {e}")
