import logging
from aiogram import Router, Bot
from aiogram.types import Message
from aiogram.filters import Filter, Command, CommandObject # Добавлено CommandObject
from aiogram.fsm.state import State, StatesGroup
from aiogram.fsm.context import FSMContext
from aiogram import exceptions as aiogram_exceptions # Добавлено
from app.localization import get_text
from app.supabase.users import get_users, get_stats, reset_user_limits, get_user_language
import os
import asyncio # Добавлено
from app.utils.error_handler import handle_error
from app.utils.formatting import convert_markdown_to_html
from aiogram.enums import ParseMode

router = Router()
logger = logging.getLogger(__name__)

# Загрузка ID администраторов из файла .env
try:
    admins_str = os.getenv('ADMINS', '')
    if admins_str:
        ADMINS = list(map(int, admins_str.split(',')))
    else:
        ADMINS = []
        logger.warning("Переменная окружения ADMINS не задана.")
except ValueError:
    ADMINS = []
    logger.error("Ошибка при загрузке ADMINS. Проверьте формат переменной окружения.")

class AdminProtect(Filter):
    def __init__(self):
        self.admins = ADMINS

    async def __call__(self, message: Message):
        return message.from_user.id in self.admins

class Newsletter(StatesGroup):
    message = State()

@router.message(AdminProtect(), Command("newsletter"))
async def admin(message: Message, state: FSMContext):
    await state.set_state(Newsletter.message)
    user_language = await get_user_language(message.from_user.id)
    request_message = get_text("newsletter", "request", user_language)
    await message.answer(request_message)

# Функция для безопасной отправки сообщения одному пользователю
async def send_message_safely(bot: Bot, user_id: int, text: str):
    try:
        await bot.send_message(
            chat_id=user_id,
            text=text,
            parse_mode=ParseMode.HTML
        )
        return True, user_id
    except aiogram_exceptions.TelegramForbiddenError:
        logger.warning(f"Пользователь {user_id} заблокировал бота.")
        return False, user_id
    except aiogram_exceptions.TelegramNotFound:
        logger.warning(f"Чат с пользователем {user_id} не найден.")
        return False, user_id
    except aiogram_exceptions.TelegramRetryAfter as e:
        logger.warning(f"Превышен лимит отправки для пользователя {user_id}. Повтор через {e.retry_after} сек.")
        await asyncio.sleep(e.retry_after)
        return await send_message_safely(bot, user_id, text) # Повторная попытка
    except aiogram_exceptions.TelegramAPIError as e:
        logger.error(f'Ошибка API при отправке сообщения пользователю {user_id}: {e}')
        return False, user_id
    except Exception as e: # Общее исключение для непредвиденных ошибок
        logger.error(f'Непредвиденная ошибка при отправке сообщения пользователю {user_id}: {e}')
        return False, user_id

@router.message(AdminProtect(), Newsletter.message)
async def get_admin(message: Message, state: FSMContext, bot: Bot):
    users = await get_users()
    if not users:
        user_language = await get_user_language(message.from_user.id)
        error_message = get_text("errors", "no_users_for_newsletter", user_language)
        await message.answer(error_message)
        await state.clear()
        return

    html_content = convert_markdown_to_html(message.text)
    tasks = [send_message_safely(bot, user["tg_id"], html_content) for user in users]
    results = await asyncio.gather(*tasks)

    successful_sends = sum(1 for success, _ in results if success)
    failed_sends = len(users) - successful_sends

    user_language = await get_user_language(message.from_user.id)
    newsletter_completed = get_text("newsletter", "completed", user_language)
    report_message = newsletter_completed.format(
        total=len(users),
        successful=successful_sends,
        failed=failed_sends
    )

    await message.answer(report_message)
    await state.clear()

@router.message(AdminProtect(), Command("stats"))
async def get_bot_stats(message: Message):
    stats = await get_stats()

    user_language = await get_user_language(message.from_user.id)
    await message.answer(
        get_text("common", "bot_stats", user_language).format(**stats),
        parse_mode="Markdown"
    )

@router.message(AdminProtect(), Command("reset_limits"))
async def reset_user_limits_handler(message: Message, command: CommandObject): # Добавлен command
    # Получаем user_id из аргументов команды
    if not command.args or len(command.args.split()) != 1: # Проверяем, есть ли аргумент и он один
        user_language = await get_user_language(message.from_user.id)
        error_message = get_text("errors", "reset_limits_usage", user_language)
        await message.answer(error_message)
        return

    try:
        user_id = int(command.args) # Получаем аргументы через command.args
        success = await reset_user_limits(user_id)

        user_language = await get_user_language(message.from_user.id)
        if success:
            success_message = get_text("success", "limits_reset", user_language)
            await message.answer(success_message.format(user_id=user_id))
        else:
            error_message = get_text("errors", "admin_user_not_found", user_language)
            await message.answer(error_message.format(user_id=user_id))

    except ValueError as ve:
        await handle_error(ve, message, "errors.invalid_user_id")
    except Exception as e:
        await handle_error(e, message, "errors.reset_limits_error", additional_info="Ошибка при сбросе лимитов")