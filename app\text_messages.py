import logging
import json
import asyncio
from typing import Dict, Any, List, Optional, Callable, Awaitable
from dotenv import load_dotenv
import aiofiles
from openai import AsyncOpenAI, OpenAIError, BadRequestError
from aiogram import Bo<PERSON>
from aiogram.types import Message
from app.supabase.messages import get_user_messages
from app.supabase.users import get_user_model, get_user_role, get_user_by_tg_id, get_user_language
from config.models_config import MODELS_CONFIG
from config.roles_config import ROLES_CONFIG, DEFAULT_ROLE_ID
from app.vision_processing import prepare_chat_history
from app.localization import get_text
# --- MCP Импорты ---
# Импортируем только необходимые типы и классы
from app.mcp_client.manager import MCPClientManager
from mcp import ClientSession, types as mcp_types
from datetime import datetime
# --- Tool Calling Импорты ---
from app.tools.registry import ToolRegistry
# -------------------

logger = logging.getLogger(__name__)
load_dotenv()

# --- Глобальный mcp_manager удален ---

async def get_system_prompt(role_id: str, user_language: str, current_time: str) -> str:
    """Асинхронное получение системного промпта из файла"""
    try:
        role_info = ROLES_CONFIG.get(role_id, ROLES_CONFIG[DEFAULT_ROLE_ID])
        prompt_filename = role_info['prompt_file']
        async with aiofiles.open(f'personas/{prompt_filename}', 'r', encoding='utf-8') as file:
            base_prompt = await file.read()
        # Формируем строку с датой, временем и языком
        # TODO: Локализовать формат даты и времени в зависимости от user_language, если это необходимо
        system_info = f"\\n\\n--- Дополнительная информация --- \\nТекущая дата и время: {current_time}\\nЯзык пользователя: {user_language}\\n--- Конец дополнительной информации ---"
        return base_prompt + system_info
    except FileNotFoundError:
        logger.error(f"Файл с промптом для роли {role_id} не найден")
        raise

async def get_mcp_tools_for_openai(manager: Optional[MCPClientManager]) -> List[Dict[str, Any]]:
    """
    Получает список инструментов от активных MCP серверов и форматирует их для OpenAI API.
    """
    if not manager:
        logger.warning("MCPClientManager не инициализирован, инструменты MCP не будут получены.")
        return []

    active_sessions = manager.get_active_sessions()
    if not active_sessions:
        logger.info("Нет активных MCP сессий, инструменты MCP не будут получены.")
        return []

    all_tools = []
    tasks = []

    for server_name, session in active_sessions.items():
        async def fetch_tools(s_name: str, s: ClientSession):
            try:
                logger.debug(f"Запрос списка инструментов у сервера '{s_name}'...")
                result: Optional[mcp_types.ListToolsResult] = await s.list_tools()
                if result and result.tools:
                     logger.info(f"Получено {len(result.tools)} инструментов от сервера '{s_name}'.")
                     formatted_tools = []
                     for tool in result.tools:
                         if not tool.name or not tool.inputSchema:
                              logger.warning(f"Инструмент от сервера '{s_name}' пропущен из-за отсутствия name или inputSchema: {tool}")
                              continue
                         if not isinstance(tool.inputSchema, dict):
                              logger.warning(f"Инструмент '{tool.name}' от сервера '{s_name}' пропущен из-за некорректного inputSchema (не словарь): {tool.inputSchema}")
                              continue
                         formatted_tools.append({
                             "type": "function",
                             "function": {
                                 "name": f"{s_name}__{tool.name}",
                                 "description": tool.description or "No description provided",
                                 "parameters": tool.inputSchema
                             }
                         })
                     return formatted_tools
                else:
                     logger.info(f"Сервер '{s_name}' не вернул инструментов или результат пуст.")
                     return []
            except Exception as e:
                logger.error(f"Ошибка при получении инструментов от сервера '{s_name}': {e}")
                return []

        tasks.append(fetch_tools(server_name, session))

    results = await asyncio.gather(*tasks)
    for tool_list in results:
        all_tools.extend(tool_list)

    if all_tools:
        logger.info(f"Всего получено {len(all_tools)} инструментов от MCP серверов.")
    else:
        logger.info("Не получено ни одного инструмента от MCP серверов.")

    return all_tools

async def call_mcp_tool(
    tool_call: Any,
    manager: Optional[MCPClientManager]
) -> Dict[str, Any]:
    """
    Вызывает инструмент MCP на соответствующем сервере и возвращает результат
    в формате сообщения для OpenAI API.
    """
    if not manager:
        return {
            "role": "tool",
            "tool_call_id": tool_call.id,
            "content": json.dumps({"error": "MCPClientManager не инициализирован"}),
        }

    full_tool_name = tool_call.function.name
    tool_arguments_str = tool_call.function.arguments

    try:
        server_name, tool_name = full_tool_name.split("__", 1)
    except ValueError:
        logger.error(f"Не удалось извлечь имя сервера и инструмента из '{full_tool_name}'")
        return {
            "role": "tool",
            "tool_call_id": tool_call.id,
            "content": json.dumps({"error": f"Неверный формат имени инструмента: {full_tool_name}"}),
        }

    active_sessions = manager.get_active_sessions()
    session = active_sessions.get(server_name)

    if not session:
        logger.error(f"Не найдена активная сессия для MCP сервера '{server_name}'")
        return {
            "role": "tool",
            "tool_call_id": tool_call.id,
            "content": json.dumps({"error": f"Сервер '{server_name}' недоступен"}),
        }

    try:
        arguments = json.loads(tool_arguments_str)
        if not isinstance(arguments, dict):
             raise ValueError("Аргументы должны быть JSON-объектом (словарем)")
        logger.info(f"Вызов инструмента '{tool_name}' на сервере '{server_name}' с аргументами: {arguments}")
    except json.JSONDecodeError:
        logger.error(f"Не удалось декодировать JSON аргументы для инструмента '{full_tool_name}': {tool_arguments_str}")
        return {
            "role": "tool",
            "tool_call_id": tool_call.id,
            "content": json.dumps({"error": "Неверный формат JSON аргументов"}),
        }
    except ValueError as e:
         logger.error(f"Ошибка валидации аргументов для инструмента '{full_tool_name}': {e}")
         return {
            "role": "tool",
            "tool_call_id": tool_call.id,
            "content": json.dumps({"error": f"Неверные аргументы: {e}"}),
        }

    try:
        result: Optional[mcp_types.CallToolResult] = await session.call_tool(tool_name, arguments)
        if result:
            content_str = ""
            if result.content:
                content_parts = []
                for item in result.content:
                    if isinstance(item, mcp_types.TextContent) and item.text: content_parts.append(item.text)
                    elif isinstance(item, mcp_types.ImageContent): content_parts.append(f"[Image: {item.mime_type}]")
                    elif isinstance(item, mcp_types.AudioContent): content_parts.append(f"[Audio: {item.mime_type}]")
                    elif isinstance(item, mcp_types.EmbeddedResource) and item.resource: content_parts.append(f"[Resource: {item.resource.uri} ({item.resource.mime_type})]")
                    else:
                         try: content_parts.append(str(item))
                         except Exception: content_parts.append("[Unsupported Content Type]")
                content_str = "\n".join(content_parts)

            if result.isError:
                 logger.warning(f"Инструмент '{full_tool_name}' вернул ошибку: {content_str}")
                 tool_result_content = json.dumps({"tool_error": content_str})
            else:
                 logger.info(f"Инструмент '{full_tool_name}' успешно выполнен.")
                 tool_result_content = content_str
            return {"role": "tool", "tool_call_id": tool_call.id, "content": tool_result_content}
        else:
            logger.error(f"Инструмент '{full_tool_name}' не вернул результат.")
            return {"role": "tool", "tool_call_id": tool_call.id, "content": json.dumps({"error": "Инструмент не вернул результат"})}
    except Exception as e:
        logger.exception(f"Ошибка при вызове инструмента '{full_tool_name}': {e}")
        return {"role": "tool", "tool_call_id": tool_call.id, "content": json.dumps({"error": f"Ошибка выполнения инструмента: {str(e)}"})}


async def call_tool_calling_tool(
    tool_call: Any,
    bot: Bot,
    message: Message
) -> Dict[str, Any]:
    """
    Вызывает инструмент Tool Calling через ToolRegistry и возвращает результат
    в формате сообщения для OpenAI API.
    """
    tool_name = tool_call.function.name
    tool_arguments_str = tool_call.function.arguments

    try:
        # Парсим аргументы
        arguments = json.loads(tool_arguments_str) if tool_arguments_str else {}
        logger.debug(f"Вызов Tool Calling инструмента '{tool_name}' с аргументами: {arguments}")

        # Получаем реестр и выполняем инструмент
        registry = ToolRegistry()
        result = await registry.execute_tool(tool_name, arguments, bot, message.chat.id)

        logger.info(f"Tool Calling инструмент '{tool_name}' выполнен успешно")
        return {
            "role": "tool",
            "tool_call_id": tool_call.id,
            "content": result
        }

    except json.JSONDecodeError as e:
        logger.error(f"Ошибка парсинга аргументов для инструмента '{tool_name}': {e}")
        return {
            "role": "tool",
            "tool_call_id": tool_call.id,
            "content": json.dumps({"error": f"Ошибка парсинга аргументов: {str(e)}"}),
        }
    except Exception as e:
        logger.error(f"Ошибка при вызове Tool Calling инструмента '{tool_name}': {e}")
        return {
            "role": "tool",
            "tool_call_id": tool_call.id,
            "content": json.dumps({"error": f"Ошибка выполнения инструмента: {str(e)}"}),
        }


# --- Изменена сигнатура: добавлены bot, message и поддержка Tool Calling ---
async def llm_response(
    question: str,
    user_id: int,
    mcp_manager: Optional[MCPClientManager],
    bot: Optional[Bot] = None,
    message: Optional[Message] = None,
    tool_usage_callback: Optional[Callable[[List[str]], Awaitable[None]]] = None,
    use_mcp_tools: bool = True,
    use_tool_calling: bool = True
) -> Dict[str, Any]:
    """
    Отправляет запрос к LLM, обрабатывает возможные вызовы инструментов MCP и Tool Calling
    и возвращает финальный ответ.

    Args:
        question: Вопрос пользователя
        user_id: ID пользователя Telegram
        mcp_manager: Менеджер MCP инструментов
        bot: Экземпляр Telegram бота (для Tool Calling)
        message: Сообщение пользователя (для Tool Calling)
        tool_usage_callback: Callback для уведомления об использовании инструментов
        use_mcp_tools: Использовать ли MCP инструменты
        use_tool_calling: Использовать ли систему Tool Calling
    """
    try:
        # --- Получение данных пользователя и истории ---
        user = await get_user_by_tg_id(user_id)
        if not user: return {"error": "Пользователь не найден"}
        messages = await get_user_messages(user["id"])
        user_role_id = await get_user_role(user_id)
        user_language = await get_user_language(user_id)
        current_time_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        try: system_prompt = await get_system_prompt(user_role_id, user_language, current_time_str)
        except FileNotFoundError: return {"error": "😅 Что-то пошло не так с настройками. Попробуйте изменить роль бота!"}
        user_model = await get_user_model(user_id)
        model_config = MODELS_CONFIG[user_model]
        supports_vision = model_config.get('vision', False)
        chat_history = prepare_chat_history(messages, system_prompt, supports_vision, question)
        openai_client = AsyncOpenAI(base_url=model_config['base_url'], api_key=model_config['api_key'])

        # --- Получение и объединение инструментов MCP и Tool Calling ---
        all_tools = []
        supports_tools = model_config.get('tools', False)

        # Получаем MCP инструменты
        mcp_tools = []
        if use_mcp_tools and supports_tools and mcp_manager:
            logger.info(f"Модель '{user_model}' поддерживает инструменты. Получение инструментов MCP...")
            mcp_tools = await get_mcp_tools_for_openai(mcp_manager)
            all_tools.extend(mcp_tools)
            logger.debug(f"Получено {len(mcp_tools)} MCP инструментов")

        # Получаем Tool Calling инструменты
        tool_calling_tools = []
        if use_tool_calling and supports_tools and bot and message:
            logger.info("Получение инструментов Tool Calling...")
            try:
                registry = ToolRegistry()
                await registry.load_tools()
                tool_calling_tools = registry.get_openai_tools_schema()
                all_tools.extend(tool_calling_tools)
                logger.debug(f"Получено {len(tool_calling_tools)} Tool Calling инструментов")
            except Exception as e:
                logger.error(f"Ошибка при загрузке Tool Calling инструментов: {e}")

        # Логирование общей информации об инструментах
        if not supports_tools:
            logger.info(f"Модель '{user_model}' не поддерживает инструменты.")
        elif not use_mcp_tools and not use_tool_calling:
            logger.info("Использование всех инструментов отключено для этого запроса.")
        else:
            logger.info(f"Всего инструментов для передачи в LLM: {len(all_tools)} (MCP: {len(mcp_tools)}, Tool Calling: {len(tool_calling_tools)})")
        # --- Конец получения инструментов ---

        # --- Первый вызов LLM ---
        logger.debug(f"Первый вызов LLM для пользователя {user_id} с историей: {chat_history}")
        if all_tools:
            tool_names = [t['function']['name'] for t in all_tools]
            logger.debug(f"Передача {len(all_tools)} инструментов в LLM: {tool_names}")

        completion_params = {
            "messages": chat_history,
            "model": user_model,
            "max_tokens": model_config.get('max_tokens', 4096),
            "temperature": model_config.get('temperature', 0.7)
        }

        if all_tools:
            completion_params["tools"] = all_tools
            completion_params["tool_choice"] = "auto"

        response = await openai_client.chat.completions.create(**completion_params)
        # --- Конец первого вызова LLM ---

        # --- Обработка вызовов инструментов ---
        response_message = response.choices[0].message
        if response_message.tool_calls:
            logger.info(f"LLM запросил вызов {len(response_message.tool_calls)} инструментов.")
            chat_history.append(response_message)

            # --- Вызываем callback для уведомления о использовании инструментов ---
            if tool_usage_callback:
                tool_names = [tool_call.function.name for tool_call in response_message.tool_calls]
                await tool_usage_callback(tool_names)
            # -----------------------------------------------------------------

            # --- Обработка вызовов инструментов (MCP и Tool Calling) ---
            tool_tasks = []

            # Получаем имена MCP и Tool Calling инструментов для различения
            mcp_tool_names = {t['function']['name'] for t in mcp_tools}
            tool_calling_names = {t['function']['name'] for t in tool_calling_tools}

            for tool_call in response_message.tool_calls:
                tool_name = tool_call.function.name

                if tool_name in mcp_tool_names:
                    # Это MCP инструмент
                    logger.debug(f"Обрабатываем MCP инструмент: {tool_name}")
                    tool_tasks.append(call_mcp_tool(tool_call, mcp_manager))

                elif tool_name in tool_calling_names:
                    # Это Tool Calling инструмент
                    logger.debug(f"Обрабатываем Tool Calling инструмент: {tool_name}")
                    if bot and message:
                        tool_tasks.append(call_tool_calling_tool(tool_call, bot, message))
                    else:
                        # Если bot или message не переданы, возвращаем ошибку
                        async def error_task():
                            return {
                                "role": "tool",
                                "tool_call_id": tool_call.id,
                                "content": json.dumps({"error": "Bot или Message не переданы для Tool Calling инструмента"})
                            }
                        tool_tasks.append(error_task())
                else:
                    # Неизвестный инструмент
                    logger.warning(f"Неизвестный инструмент: {tool_name}")
                    async def unknown_tool_task():
                        return {
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "content": json.dumps({"error": f"Неизвестный инструмент: {tool_name}"})
                        }
                    tool_tasks.append(unknown_tool_task())

            # Выполняем все задачи параллельно
            tool_results = await asyncio.gather(*tool_tasks)
            for tool_result_message in tool_results:
                chat_history.append(tool_result_message)
                logger.debug(f"Добавлен результат инструмента в историю: {tool_result_message}")
            # --- Конец обработки инструментов ---

            # --- Второй вызов LLM с результатами инструментов ---
            logger.info("Второй вызов LLM с результатами инструментов...")
            logger.debug(f"История для второго вызова LLM: {chat_history}")
            second_call_params = {"messages": chat_history, "model": user_model, "max_tokens": model_config.get('max_tokens', 4096), "temperature": model_config.get('temperature', 0.7)}
            response = await openai_client.chat.completions.create(**second_call_params)
            logger.info("Второй вызов LLM завершен.")
            # --- Конец второго вызова LLM ---

        return response

    # --- Обработка ошибок (без изменений) ---
    except BadRequestError as e:
        logging.error(f"OpenAI BadRequestError: {e}")
        is_content_policy_violation = False
        error_str = str(e)
        if any(phrase in error_str.lower() for phrase in ['content filter', 'content management policy', 'content_filter']): is_content_policy_violation = True
        elif hasattr(e, 'error') and e.error:
            if e.error.get('code') == 'content_filter': is_content_policy_violation = True
            elif isinstance(e.error, dict) and e.error.get('message') and 'content management policy' in str(e.error.get('message')).lower(): is_content_policy_violation = True
        if is_content_policy_violation:
            logging.warning(f"Запрос отклонен из-за нарушения политики контента: {question[:100]}...")
            user_language = await get_user_language(user_id)
            error_message = get_text("errors", "content_filter", user_language)
        else:
            logging.error(f"Другая ошибка BadRequest: {e}")
            error_message = "🤔 Кажется, что-то не так с запросом. Может быть, стоит переформулировать?"
        return {"error": error_message}
    except OpenAIError as e:
        logging.error(f"Ошибка OpenAI: {e}")
        user_language = await get_user_language(user_id)
        error_message = get_text("errors", "generation_error_simple", user_language)
        error_str = str(e).lower()
        if '429' in error_str or 'too many requests' in error_str or 'rate limit' in error_str:
            logging.warning(f"Обнаружена ошибка 429 Too Many Requests в тексте ошибки: {str(e)[:200]}")
            error_message = get_text("errors", "too_many_requests", user_language)
        if hasattr(e, 'error') and e.error:
            if e.error.get('code') == 'invalid_api_key': error_message = "🔧 У нас технические неполадки. Мы уже работаем над их устранением!"
            elif e.error.get('code') == 'rate_limit_exceeded': error_message = get_text("errors", "too_many_requests", user_language)
            elif e.error.get('code') == 'invalid_request_error': error_message = "🤔 Кажется, что-то не так с запросом. Может быть, стоит переформулировать?"
        if hasattr(e, 'status'):
            if e.status == 429 or '429' in str(e) or 'too many requests' in str(e).lower():
                logging.warning(f"Обнаружена ошибка 429 Too Many Requests: {str(e)[:200]}")
                error_message = get_text("errors", "too_many_requests", user_language)
            elif e.status == 500 and 'details' in str(e) and 'azure-openai error' in str(e) and 'content management policy' in str(e):
                logging.warning(f"Обнаружено нарушение политики контента через Portkey Gateway: {question[:100]}...")
                error_message = get_text("errors", "content_filter", user_language)
        return {"error": error_message}
    except Exception as e:
        logging.exception(f"Непредвиденная ошибка при обработке запроса для user_id {user_id}: {e}")
        user_language = await get_user_language(user_id)
        error_message = get_text("errors", "unexpected_error", user_language)
        return {"error": error_message}