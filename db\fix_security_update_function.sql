-- =============================================
-- ИСПРАВЛЕНИЕ БЕЗОПАСНОСТИ ФУНКЦИИ update_updated_at_column
-- =============================================
-- Этот скрипт исправляет проблему безопасности с функцией update_updated_at_column,
-- добавляя правильную настройку search_path для предотвращения атак через
-- подмену схемы.
--
-- Инструкция по использованию:
-- 1. Войдите в панель управления Supabase (https://app.supabase.io)
-- 2. Выберите ваш проект
-- 3. Перейдите в раздел "SQL Editor"
-- 4. Создайте новый запрос, вставьте содержимое этого файла
-- 5. Выполните запрос
--
-- ВАЖНО: Этот скрипт безопасно заменяет существующую функцию.

-- Установка часового пояса для текущей сессии
SET timezone TO 'Europe/Moscow';

-- =============================================
-- ЧАСТЬ 1: УДАЛЕНИЕ СТАРОЙ ФУНКЦИИ
-- =============================================

-- Сначала удаляем триггер, который использует функцию
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;

-- Удаляем старую функцию
DROP FUNCTION IF EXISTS public.update_updated_at_column();

-- =============================================
-- ЧАСТЬ 2: СОЗДАНИЕ БЕЗОПАСНОЙ ФУНКЦИИ
-- =============================================

-- Создание безопасной функции с правильными настройками безопасности
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Обновляем поле updated_at текущим временем в московском часовом поясе
    NEW.updated_at = timezone('Europe/Moscow', NOW());
    RETURN NEW;
END;
$$;

-- =============================================
-- ЧАСТЬ 3: НАСТРОЙКА ПРАВ ДОСТУПА
-- =============================================

-- Устанавливаем владельца функции
ALTER FUNCTION public.update_updated_at_column() OWNER TO postgres;

-- Добавляем комментарий к функции
COMMENT ON FUNCTION public.update_updated_at_column() IS 
'Безопасная функция для автоматического обновления поля updated_at при изменении записи. 
Использует SECURITY DEFINER и фиксированный search_path для предотвращения атак через подмену схемы.';

-- =============================================
-- ЧАСТЬ 4: ВОССТАНОВЛЕНИЕ ТРИГГЕРА
-- =============================================

-- Создаем триггер заново с использованием безопасной функции
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Добавляем комментарий к триггеру
COMMENT ON TRIGGER update_users_updated_at ON public.users IS 
'Триггер для автоматического обновления поля updated_at при изменении записи пользователя.';

-- =============================================
-- ЧАСТЬ 5: ПРОВЕРКА БЕЗОПАСНОСТИ
-- =============================================

-- Проверяем, что функция создана с правильными настройками
SELECT 
    proname as function_name,
    prosecdef as security_definer,
    proconfig as function_config
FROM pg_proc 
WHERE proname = 'update_updated_at_column' 
    AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

-- =============================================
-- ПРИМЕЧАНИЯ ПО БЕЗОПАСНОСТИ
-- =============================================
-- 1. SECURITY DEFINER - функция выполняется с правами владельца (postgres)
-- 2. SET search_path = public - фиксирует схему поиска, предотвращая атаки
-- 3. Функция использует только встроенные PostgreSQL функции
-- 4. Триггер привязан только к таблице users
-- 5. Функция не принимает внешние параметры, что исключает SQL-инъекции
-- =============================================

-- Вывод сообщения об успешном завершении
DO $$
BEGIN
    RAISE NOTICE 'Функция update_updated_at_column успешно обновлена с улучшенными настройками безопасности.';
    RAISE NOTICE 'Триггер update_users_updated_at восстановлен.';
    RAISE NOTICE 'Проверьте результаты выше, чтобы убедиться в правильности настроек.';
END $$;
