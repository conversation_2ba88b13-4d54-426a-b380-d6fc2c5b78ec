# app/services/image_generation/providers/pollinations.py
import asyncio
import logging
import os
import random
import urllib.parse
from typing import Any, Dict, Optional

import aiohttp
from app.services.image_generation.base import ImageGenerator
from config.image_generation_config import (
    IMAGE_GENERATION_SETTINGS,
    get_image_dimensions,
)

logger = logging.getLogger(__name__)

class PollinationsGenerator(ImageGenerator):
    """
    Провайдер для генерации изображений через API Pollinations.ai.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Инициализирует провайдер с его конфигурацией.
        """
        self.config = config
        self.base_url = self.config.get("base_url")
        self.timeout = self.config.get("timeout", 30)
        self.max_retries = self.config.get("max_retries", 3)
        self.retry_delay = self.config.get("retry_delay", 10)

        # Получаем токен и реферер из переменных окружения, имена которых указаны в конфиге
        self.api_token = os.getenv(self.config.get("api_token_env", ""))
        self.referrer = os.getenv(self.config.get("referrer_env", ""))

        if not self.base_url:
            raise ValueError("В конфигурации для PollinationsGenerator отсутствует 'base_url'")

    def _sanitize_prompt(self, prompt: str) -> str:
        """
        Очищает и подготавливает промпт для API.
        """
        max_len = IMAGE_GENERATION_SETTINGS.get("max_prompt_length", 200)
        cleaned = " ".join(prompt.split())
        if len(cleaned) > max_len:
            cleaned = cleaned[:max_len].rsplit(" ", 1)[0]
        return urllib.parse.quote(cleaned)

    async def generate(
        self, prompt: str, size_key: str, retry_count: int = 0
    ) -> Optional[bytes]:
        """
        Генерирует изображение, реализуя "контракт" ImageGenerator.
        """
        try:
            cleaned_prompt = self._sanitize_prompt(prompt)
            if not cleaned_prompt:
                return None

            width, height = get_image_dimensions(size_key)

            params = {
                "width": width,
                "height": height,
                "seed": random.randint(1, 999999),
                "model": self.config.get("model_name_api", "flux"), # Используем модель из конфига
                **self.config.get("default_params", {}), # <-- ИЗМЕНЕНИЕ ЗДЕСЬ
            }

            if self.api_token:
                params["token"] = self.api_token

            query_params = "&".join(f"{k}={v}" for k, v in params.items())
            url = f"{self.base_url}/{cleaned_prompt}?{query_params}"

            headers = {}
            if self.api_token:
                headers["Authorization"] = f"Bearer {self.api_token}"
            if self.referrer:
                headers["Referer"] = self.referrer

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url, headers=headers, timeout=self.timeout
                ) as response:
                    if response.status == 200:
                        return await response.read()

                    if retry_count < self.max_retries:
                        logger.warning(
                            f"Ошибка API Pollinations (статус: {response.status}). "
                            f"Попытка #{retry_count + 1} через {self.retry_delay} сек."
                        )
                        await asyncio.sleep(self.retry_delay)
                        return await self.generate(prompt, size_key, retry_count + 1)
                    else:
                        logger.error(
                            f"Все {self.max_retries} попыток исчерпаны. "
                            f"Финальная ошибка API Pollinations (статус: {response.status})."
                        )
                        return None

        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            logger.error(f"Сетевая ошибка при обращении к Pollinations: {e}")
            if retry_count < self.max_retries:
                await asyncio.sleep(self.retry_delay)
                return await self.generate(prompt, size_key, retry_count + 1)
            return None
        except Exception as e:
            logger.exception(f"Неожиданная ошибка в PollinationsGenerator: {e}")
            return None


