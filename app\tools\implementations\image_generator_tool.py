"""
Инструмент для генерации изображений через Tool Calling.

Использует новую модульную архитектуру генерации изображений.
Реализует протокол Tool для интеграции с системой Tool Calling.
"""

import logging
from typing import Optional
from aiogram import Bot
from aiogram.types import BufferedInputFile

from app.tools.base_tool import Tool
from app.supabase.users import get_user_image_model, get_user_language, set_active_image_file_id
from app.localization import get_text
from app.services.image_generation.manager import get_generator
from config.image_generation_config import IMAGE_MODELS_CONFIG

logger = logging.getLogger(__name__)


class ImageGeneratorTool:
    """
    Инструмент для генерации изображений через Tool Calling.
    
    Реализует протокол Tool для интеграции с системой Tool Calling.
    """
    
    # Обязательные атрибуты протокола Tool
    name: str = "generate_image"
    description: str = "Generates an image based on a user's text description and desired aspect ratio. Use this when the user asks to draw, create, or generate a picture."
    schema: dict = {
        "type": "object",
        "properties": {
            "prompt": {
                "type": "string",
                "description": "A detailed and vivid text description of the image to be generated. IMPORTANT: Always write the description in English, regardless of the user's language."
            },
            "size": {
                "type": "string",
                "description": "The aspect ratio of the image.",
                "enum": ["1:1", "16:9", "9:16", "7:4"],
                "default": "1:1"
            }
        },
        "required": ["prompt"]
    }
    
    async def execute(self, bot: Bot, chat_id: int, prompt: str, size: str = '1:1') -> str:
        """
        Выполняет генерацию изображения и отправляет его пользователю.

        Args:
            bot: Экземпляр Telegram бота
            chat_id: ID чата для отправки изображения
            prompt: Описание изображения для генерации
            size: Соотношение сторон изображения

        Returns:
            str: Результат выполнения для LLM
        """
        try:
            # Получаем user_id из chat_id (в нашем случае они совпадают)
            user_id = chat_id

            # Получаем модель изображений пользователя и создаем генератор
            user_image_model = await get_user_image_model(user_id)
            generator = get_generator(user_image_model)

            # Генерируем изображение
            image_bytes = await generator.generate(prompt, size)

            if image_bytes:
                # Получаем язык пользователя для локализации
                user_language = await get_user_language(user_id)

                # Формируем локализованную подпись
                model_config = IMAGE_MODELS_CONFIG[user_image_model]
                model_name = model_config["name"]
                caption_template = get_text("common", "tools", user_language)["model_caption"]
                caption = caption_template.format(model_name=model_name)

                # Отправляем изображение пользователю и получаем объект сообщения
                photo = BufferedInputFile(image_bytes, filename="generated_image.png")
                sent_message = await bot.send_photo(chat_id=chat_id, photo=photo, caption=caption)

                # Сохраняем file_id как активный контекст для редактирования
                if sent_message and sent_message.photo:
                    image_file_id = sent_message.photo[-1].file_id
                    await set_active_image_file_id(user_id, image_file_id)
                    logger.info(f"Контекст изображения (Tool) обновлен для {user_id}")

                logger.info(f"Изображение успешно сгенерировано и отправлено пользователю {user_id}")
                return "Image was successfully generated and sent to the user."
            else:
                logger.error(f"Не удалось сгенерировать изображение для пользователя {user_id}")
                return "Failed to generate the image due to an internal error."

        except Exception as e:
            logger.error(f"Ошибка при выполнении инструмента генерации изображений: {e}")
            return "Failed to generate the image due to an internal error."
