# app/services/image_generation/base.py
from typing import Protocol, Optional, Dict, Any

class ImageGenerator(Protocol):
    """
    Протокол (интерфейс) для всех провайдеров генерации изображений.
    Каждый провайдер должен будет реализовать этот "контракт".
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Инициализатор, принимающий специфичную для провайдера конфигурацию.
        """
        ...

    async def generate(self, prompt: str, size_key: str) -> Optional[bytes]:
        """
        Основной метод для генерации изображения.

        Args:
            prompt: Текстовое описание изображения.
            size_key: К<PERSON>юч размера из конфига (например, '1:1', '16:9').

        Returns:
            Байты сгенерированного изображения или None в случае ошибки.
        """
        ...
