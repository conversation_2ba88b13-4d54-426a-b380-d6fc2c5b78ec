-- =============================================
-- СКРИПТ ИНИЦИАЛИЗАЦИИ БАЗЫ ДАННЫХ SUPABASE
-- =============================================
-- Этот скрипт создает необходимые таблицы, индексы, триггеры и политики безопасности
-- для работы Telegram-бота с базой данных Supabase.
--
-- Инструкция по использованию:
-- 1. Войдите в панель управления Supabase (https://app.supabase.io)
-- 2. Выберите ваш проект
-- 3. Перейдите в раздел "SQL Editor"
-- 4. Создайте новый запрос, вставьте содержимое этого файла
-- 5. Выполните запрос
--
-- ВАЖНО: Выполняйте этот скрипт только на новой базе данных!
-- Выполнение на существующей базе может привести к потере данных.

-- Установка часового пояса Москвы (MSK, UTC+3) для базы данных
ALTER DATABASE postgres SET timezone TO 'Europe/Moscow';
-- Установка часового пояса для текущей сессии
SET timezone TO 'Europe/Moscow';

-- =============================================
-- ЧАСТЬ 1: СОЗДАНИЕ ТАБЛИЦ И ИНДЕКСОВ
-- =============================================

-- Создание таблицы пользователей
CREATE TABLE public.users (
    id BIGSERIAL PRIMARY KEY,
    tg_id BIGINT NOT NULL UNIQUE,
    model VARCHAR NOT NULL,
    role VARCHAR NOT NULL,
    referral_code VARCHAR NOT NULL UNIQUE,
    referrer_id BIGINT REFERENCES public.users(id),
    referral_count INTEGER NOT NULL DEFAULT 0,
    subscription_plan VARCHAR NOT NULL,
    subscription_end_date TIMESTAMP WITH TIME ZONE,
    text_requests_today INTEGER NOT NULL DEFAULT 0,
    image_requests_today INTEGER NOT NULL DEFAULT 0,
    last_request_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('Europe/Moscow', NOW()),
    language VARCHAR(10) NOT NULL DEFAULT 'ru',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('Europe/Moscow', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('Europe/Moscow', NOW())
);

-- Создание таблицы сообщений
CREATE TABLE public.messages (
    id BIGSERIAL PRIMARY KEY,
    role VARCHAR NOT NULL,
    content TEXT NOT NULL, -- Используем TEXT для поддержки больших сообщений
    user_id BIGINT NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    image_data TEXT, -- Используем TEXT для поддержки больших данных изображений
    caption TEXT, -- Используем TEXT для поддержки длинных подписей
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('Europe/Moscow', NOW())
);

-- Создание индексов для оптимизации запросов
CREATE INDEX idx_users_tg_id ON public.users(tg_id);
CREATE INDEX idx_users_referral_code ON public.users(referral_code);
CREATE INDEX idx_messages_user_id ON public.messages(user_id);

-- =============================================
-- ЧАСТЬ 2: СОЗДАНИЕ ТРИГГЕРОВ
-- =============================================

-- Создание триггера для автоматического обновления updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('Europe/Moscow', NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON public.users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- ЧАСТЬ 3: НАСТРОЙКА ROW LEVEL SECURITY (RLS)
-- =============================================

-- Включение RLS для таблиц
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Политики для таблицы users
-- Разрешаем анонимному ключу выполнять все операции с таблицей users
CREATE POLICY "Allow anon access to users" ON public.users
    FOR ALL
    TO anon
    USING (true)
    WITH CHECK (true);

-- Политики для таблицы messages
-- Разрешаем анонимному ключу выполнять все операции с таблицей messages
CREATE POLICY "Allow anon access to messages" ON public.messages
    FOR ALL
    TO anon
    USING (true)
    WITH CHECK (true);

-- =============================================
-- ЧАСТЬ 4: ДОБАВЛЕНИЕ КОММЕНТАРИЕВ К ТАБЛИЦАМ И СТОЛБЦАМ
-- =============================================

-- Комментарии к таблице users
COMMENT ON TABLE public.users IS 'Пользователи Telegram-бота';
COMMENT ON COLUMN public.users.tg_id IS 'ID пользователя в Telegram';
COMMENT ON COLUMN public.users.model IS 'Модель AI, используемая пользователем';
COMMENT ON COLUMN public.users.role IS 'Роль пользователя в системе';
COMMENT ON COLUMN public.users.referral_code IS 'Реферальный код пользователя';
COMMENT ON COLUMN public.users.referrer_id IS 'ID пользователя, пригласившего текущего пользователя';
COMMENT ON COLUMN public.users.subscription_plan IS 'План подписки пользователя';
COMMENT ON COLUMN public.users.text_requests_today IS 'Количество текстовых запросов за сегодня';
COMMENT ON COLUMN public.users.image_requests_today IS 'Количество запросов на генерацию изображений за сегодня';
COMMENT ON COLUMN public.users.last_request_date IS 'Дата и время последнего запроса';
COMMENT ON COLUMN public.users.language IS 'Язык интерфейса пользователя (ru, en)';
COMMENT ON COLUMN public.users.created_at IS 'Дата и время создания записи';
COMMENT ON COLUMN public.users.updated_at IS 'Дата и время последнего обновления записи';

-- Комментарии к таблице messages
COMMENT ON TABLE public.messages IS 'Сообщения пользователей и ответы бота';
COMMENT ON COLUMN public.messages.role IS 'Роль отправителя сообщения (user/assistant)';
COMMENT ON COLUMN public.messages.content IS 'Текстовое содержимое сообщения';
COMMENT ON COLUMN public.messages.user_id IS 'ID пользователя, связанного с сообщением';
COMMENT ON COLUMN public.messages.image_data IS 'Данные изображения (если есть)';
COMMENT ON COLUMN public.messages.caption IS 'Подпись к изображению (если есть)';
COMMENT ON COLUMN public.messages.created_at IS 'Дата и время создания сообщения';

-- =============================================
-- ПРИМЕЧАНИЕ ПО БЕЗОПАСНОСТИ
-- =============================================
-- В реальном проекте следует настроить более строгие политики безопасности.
-- Текущие политики разрешают полный доступ анонимному ключу для упрощения разработки.
-- В продакшн-версии рекомендуется ограничить доступ в соответствии с бизнес-логикой приложения.
-- =============================================