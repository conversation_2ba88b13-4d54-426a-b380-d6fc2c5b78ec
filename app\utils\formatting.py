# app/utils/formatting.py
"""
Модуль форматирования текста для Telegram.
Предоставляет утилиты для конвертации Markdown в HTML формат, совместимый с Telegram API.
"""
import re
from typing import Dict, List, Optional, Tuple, Union
import logging

from aiogram import html
from bs4 import BeautifulSoup, Tag, NavigableString
from markdown_it import MarkdownIt

# Настройка логгера
logger = logging.getLogger(__name__)

# Инициализация парсера Markdown с оптимальными настройками
md_parser = MarkdownIt("commonmark", {"breaks": True, "html": True})

# Константы для улучшения читаемости кода
TELEGRAM_SUPPORTED_TAGS = {
    "b", "strong", "i", "em", "u", "ins", "s", "strike", "del", 
    "a", "code", "pre", "blockquote"
}

TAG_MAPPING = {
    "strike": "s",
    "strong": "b",
    "em": "i",
    "__": "u",  # Для подчеркнутого текста (двойное подчеркивание)
    "~~": "s",  # Для зачеркнутого текста (двойная тильда)
}

# Словарь для замены HTML-сущностей
HTML_ENTITIES = {
    "<": "&lt;",
    ">": "&gt;",
    "&": "&amp;",
}

class MarkdownConverter:
    """
    Класс для конвертации Markdown в Telegram-совместимый HTML.
    Обеспечивает корректную обработку вложенных структур, списков и специальных элементов.
    """
    
    @staticmethod
    def convert(md_text: str) -> str:
        """
        Конвертирует Markdown в HTML формат, совместимый с Telegram.
        
        Args:
            md_text: Исходный текст в формате Markdown.
            
        Returns:
            Текст в формате HTML, пригодный для отправки через Telegram API.
        """
        if not md_text:
            return ""
            
        try:
            # Предварительная обработка некоторых типов форматирования
            md_text = MarkdownConverter._preprocess_markdown(md_text)
            
            html_markup = md_parser.render(md_text)
            soup = BeautifulSoup(html_markup, "html.parser")
            
            converter = MarkdownConverter()
            result = converter._process_root_nodes(soup)
            
            # Нормализация пустых строк
            result = re.sub(r'(\n\s*){3,}', '\n\n', result)
            
            # Пост-обработка для особых случаев
            result = MarkdownConverter._postprocess_html(result)
            
            return result.strip()
        except Exception as e:
            logger.error(f"Ошибка конвертации Markdown в HTML: {e}", exc_info=True)
            return html.quote(md_text)
    
    @staticmethod
    def _preprocess_markdown(text: str) -> str:
        """
        Предварительная обработка markdown текста для правильного форматирования.
        """
        # Обработка двойных подчеркиваний для подчеркнутого текста
        text = re.sub(r'__([^_]+)__', r'<u>\1</u>', text)
        
        # Обработка зачеркнутого текста
        text = re.sub(r'~~([^~]+)~~', r'<s>\1</s>', text)
        
        return text
    
    @staticmethod
    def _postprocess_html(html_text: str) -> str:
        """
        Пост-обработка HTML для исправления специфичных проблем.
        """
        # Исправление тегов pre и code для соответствия ожиданиям Telegram
        html_text = re.sub(r'<pre><code>(.*?)</code></pre>', r'<pre>\1</pre>', html_text, flags=re.DOTALL)
        
        return html_text
    
    @staticmethod
    def _escape_html(text: str) -> str:
        """
        Экранирует HTML-сущности в тексте.
        """
        for char, entity in HTML_ENTITIES.items():
            text = text.replace(char, entity)
        return text
    
    def _process_root_nodes(self, soup: BeautifulSoup) -> str:
        """Обрабатывает корневые узлы документа."""
        if soup.body:
            return "".join(self._render_node(child) for child in soup.body.contents)
        return "".join(self._render_node(child) for child in soup.contents)
    
    def _render_node(self, 
                    node: Union[Tag, NavigableString], 
                    in_list: bool = False, 
                    list_type: Optional[Union[str, Tuple[str, int]]] = None, 
                    depth: int = 0) -> str:
        """
        Рекурсивно обрабатывает узел и его дочерние элементы.
        
        Args:
            node: Текущий узел для обработки.
            in_list: Находится ли узел внутри списка.
            list_type: Тип списка и счетчик (для нумерованных списков).
            depth: Глубина вложенности для списков.
            
        Returns:
            Отформатированная HTML-строка для текущего узла.
        """
        # Обработка текстовых узлов
        if isinstance(node, NavigableString):
            text = str(node)
            # Внутри code и pre сохраняем форматирование как есть
            if node.parent and node.parent.name in ("code", "pre"):
                return text
            # Экранируем HTML-сущности в обычном тексте
            return self._escape_html(text)

        # Обработка HTML-тегов
        tag_name = node.name
        
        # Обработка базовых текстовых тегов форматирования
        if tag_name in TELEGRAM_SUPPORTED_TAGS.intersection({"b", "strong", "i", "em", "u", "ins", "s", "strike", "del", "code"}):
            return self._process_formatting_tag(node, tag_name, in_list, list_type, depth)
            
        # Обработка параграфов
        if tag_name == "p":
            return self._process_paragraph(node, in_list, list_type, depth)
            
        # Обработка заголовков
        if tag_name in {"h1", "h2", "h3", "h4", "h5", "h6"}:
            return self._process_heading(node, in_list, list_type, depth)
            
        # Обработка списков
        if tag_name == "ul":
            return self._process_unordered_list(node, depth)
            
        if tag_name == "ol":
            return self._process_ordered_list(node, depth)
            
        if tag_name == "li":
            return self._process_list_item(node, list_type, depth)
            
        # Обработка блоков цитирования
        if tag_name == "blockquote":
            return self._process_blockquote(node, in_list, list_type, depth)
            
        # Обработка блоков кода
        if tag_name == "pre":
            return self._process_pre(node, in_list, list_type, depth)
            
        # Обработка ссылок
        if tag_name == "a":
            return self._process_link(node, in_list, list_type, depth)
            
        # Обработка изображений
        if tag_name == "img":
            return self._process_image(node)
            
        # По умолчанию обрабатываем дочерние элементы
        return "".join(self._render_node(child, in_list, list_type, depth) for child in node.children)
        
    def _process_formatting_tag(self, node: Tag, tag_name: str, in_list: bool, list_type: Optional[Union[str, Tuple[str, int]]], depth: int) -> str:
        """Обрабатывает теги форматирования текста."""
        # Сопоставление тегов с их эквивалентами в Telegram
        open_tag = f"<{TAG_MAPPING.get(tag_name, tag_name)}>"
        close_tag = open_tag.replace('<', '</')
        
        content = "".join(self._render_node(child, in_list, list_type, depth) for child in node.children)
        return f"{open_tag}{content}{close_tag}"
    
    def _process_paragraph(self, node: Tag, in_list: bool, list_type: Optional[Union[str, Tuple[str, int]]], depth: int) -> str:
        """Обрабатывает параграфы."""
        content = "".join(self._render_node(child, in_list, list_type, depth) for child in node.children).strip()
        return content + ("\n" if not in_list and content else "")
    
    def _process_heading(self, node: Tag, in_list: bool, list_type: Optional[Union[str, Tuple[str, int]]], depth: int) -> str:
        """Обрабатывает заголовки."""
        content = "".join(self._render_node(child, in_list, list_type, depth) for child in node.children)
        return f"<b>{content}</b>\n"
    
    def _process_unordered_list(self, node: Tag, depth: int) -> str:
        """Обрабатывает неупорядоченные списки."""
        result = []
        for li in node.find_all("li", recursive=False):
            result.append(self._render_node(li, in_list=True, list_type="ul", depth=depth + 1))
        return "".join(result)
    
    def _process_ordered_list(self, node: Tag, depth: int) -> str:
        """Обрабатывает упорядоченные списки."""
        result = []
        for idx, li in enumerate(node.find_all("li", recursive=False), start=1):
            result.append(self._render_node(li, in_list=True, list_type=("ol", idx), depth=depth + 1))
        return "".join(result)
    
    def _process_list_item(self, node: Tag, list_type: Optional[Union[str, Tuple[str, int]]], depth: int) -> str:
        """Обрабатывает элементы списка."""
        indent = "    " * (depth - 1) if depth > 0 else ""
        
        # Определение маркера списка
        if isinstance(list_type, tuple) and list_type[0] == "ol":
            marker = f"{list_type[1]}."
        else:
            marker = "•"
        
        # Обработка содержимого элемента списка
        content = "".join(self._render_node(child, True, None, depth) for child in node.children).strip()
        
        # Обработка вложенных списков
        sublists = []
        for sub in node.find_all(["ul", "ol"], recursive=False):
            sublists.append(self._render_node(sub, in_list=True, list_type=None, depth=depth + 1))
            sub.extract()
        
        sub_content = "".join(sublists)
        line = f"{indent}{marker} {content}".rstrip()
        
        if sub_content:
            line += "\n" + sub_content
            
        return line + "\n"
    
    def _process_blockquote(self, node: Tag, in_list: bool, list_type: Optional[Union[str, Tuple[str, int]]], depth: int) -> str:
        """Обрабатывает блоки цитирования."""
        content = "".join(self._render_node(child, in_list, list_type, depth) for child in node.children)
        return f"<blockquote>{content.strip()}</blockquote>\n"
    
    def _process_pre(self, node: Tag, in_list: bool, list_type: Optional[Union[str, Tuple[str, int]]], depth: int) -> str:
        """Обрабатывает блоки кода."""
        # Если внутри pre есть code, извлекаем содержимое code
        code_node = node.find("code")
        if code_node:
            code_content = "".join(self._render_node(child, in_list, list_type, depth) for child in code_node.children)
            return f"<pre>{code_content.strip()}</pre>\n"
        
        # Иначе обрабатываем содержимое pre напрямую
        code_content = "".join(self._render_node(child, in_list, list_type, depth) for child in node.children)
        return f"<pre>{code_content.strip()}</pre>\n"
    
    def _process_link(self, node: Tag, in_list: bool, list_type: Optional[Union[str, Tuple[str, int]]], depth: int) -> str:
        """Обрабатывает ссылки."""
        href = node.get("href")
        content = "".join(self._render_node(child, in_list, list_type, depth) for child in node.children)
        
        if href:
            return f'<a href="{html.quote(href)}">{content}</a>'
        return content
    
    def _process_image(self, node: Tag) -> str:
        """Обрабатывает изображения (конвертирует в текстовое представление)."""
        alt = node.get("alt", "")
        src = node.get("src", "")
        
        res = alt if alt else "image"
        if src:
            res += f" ({src})"
            
        return res

def convert_markdown_to_html(md_text: str) -> str:
    """
    Конвертирует Markdown в HTML формат, совместимый с Telegram.
    
    Поддерживает:
    - Базовое форматирование: жирный, курсив, подчеркнутый текст
    - Ссылки
    - Блоки кода и встроенный код
    - Упорядоченные и неупорядоченные списки с вложенностью
    - Блоки цитирования
    - Заголовки
    - Текстовые представления изображений
    
    Args:
        md_text: Исходный текст в формате Markdown
        
    Returns:
        Текст в формате HTML, пригодный для отправки через Telegram API
    """
    return MarkdownConverter.convert(md_text)
