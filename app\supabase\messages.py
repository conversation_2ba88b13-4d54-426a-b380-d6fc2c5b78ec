import os
import logging
import asyncio
from typing import Dict, List, Optional, Any
from app.supabase.client import supabase_client

# Настройка логирования
logger = logging.getLogger(__name__)

# Константа для максимального количества изображений в истории vision
MAX_VISION_IMAGES = int(os.getenv("MAX_VISION_IMAGES", 3))

async def save_message(role: str, content: str, user_id: int, image_data: Optional[str] = None, caption: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Сохраняет сообщение в базе данных.

    Args:
        role: Роль отправителя сообщения (user, assistant, system)
        content: Текст сообщения
        user_id: ID пользователя
        image_data: Данные изображения (если есть)
        caption: Подпись к изображению (если есть)

    Returns:
        Optional[Dict[str, Any]]: Данные сохраненного сообщения или None в случае ошибки
    """
    try:
        message_data = {
            "role": role,
            "content": content,
            "user_id": user_id,
            "image_data": image_data,
            "caption": caption
        }

        response = await asyncio.to_thread(
            supabase_client.table("messages").insert(message_data).execute
        )

        if response.data and len(response.data) > 0:
            # Проверяем, не превышено ли максимальное количество сообщений
            await _cleanup_old_messages(user_id)
            return response.data[0]
        return None
    except Exception as e:
        logger.error(f"Ошибка при сохранении сообщения для пользователя {user_id}: {str(e)}")
        return None

async def _cleanup_old_messages(user_id: int) -> None:
    """
    Удаляет старые сообщения, если их количество превышает максимальное.

    Args:
        user_id: ID пользователя
    """
    try:
        max_messages = int(os.getenv("MAX_MESSAGES", 30))

        # Получаем количество сообщений пользователя
        count_response = await asyncio.to_thread(
            supabase_client.table("messages").select("count", count="exact").eq("user_id", user_id).execute
        )
        message_count = count_response.count if hasattr(count_response, 'count') else 0

        if message_count > max_messages:
            # Вычисляем, сколько сообщений нужно удалить
            messages_to_delete = message_count - max_messages

            # Получаем ID самых старых сообщений для удаления
            oldest_messages_response = await asyncio.to_thread(
                supabase_client.table("messages").select("id").eq("user_id", user_id).order("id").limit(messages_to_delete).execute
            )

            if oldest_messages_response.data and len(oldest_messages_response.data) > 0:
                # Извлекаем ID сообщений
                message_ids = [message["id"] for message in oldest_messages_response.data]

                # Удаляем сообщения
                await asyncio.to_thread(
                    supabase_client.table("messages").delete().in_("id", message_ids).execute
                )
    except Exception as e:
        logger.error(f"Ошибка при очистке старых сообщений для пользователя {user_id}: {str(e)}")

async def get_user_messages(user_id: int) -> List[Dict[str, Any]]:
    """
    Получает сообщения пользователя.

    Args:
        user_id: ID пользователя в Supabase (не tg_id)

    Returns:
        List[Dict[str, Any]]: Список сообщений
    """
    try:
        logger.debug(f"Получение сообщений для пользователя с ID {user_id}")

        # Получаем все текстовые сообщения (без изображений)
        text_messages_response = await asyncio.to_thread(
            supabase_client.table("messages").select("*").eq("user_id", user_id).is_("image_data", "null").execute
        )
        text_messages = text_messages_response.data if text_messages_response.data else []
        logger.debug(f"Получено {len(text_messages)} текстовых сообщений")

        # Получаем только последние MAX_VISION_IMAGES изображений
        image_messages_response = await asyncio.to_thread(
            supabase_client.table("messages").select("*").eq("user_id", user_id).not_.is_("image_data", "null").order("id", desc=True).limit(MAX_VISION_IMAGES).execute
        )
        image_messages = image_messages_response.data if image_messages_response.data else []
        logger.debug(f"Получено {len(image_messages)} сообщений с изображениями")

        # Объединяем результаты
        all_messages = text_messages + image_messages

        # Сортируем по ID для сохранения хронологического порядка
        all_messages.sort(key=lambda msg: msg["id"])

        logger.debug(f"Всего получено {len(all_messages)} сообщений")
        return all_messages
    except Exception as e:
        logger.error(f"Ошибка при получении сообщений пользователя {user_id}: {str(e)}")
        return []

async def delete_user_messages(user_id: int) -> bool:
    """
    Удаляет все сообщения пользователя.

    Args:
        user_id: ID пользователя в Supabase (не tg_id)

    Returns:
        bool: True, если удаление прошло успешно, иначе False
    """
    try:
        logger.debug(f"Удаление всех сообщений для пользователя с ID {user_id}")

        # Проверяем, есть ли сообщения для удаления
        count_response = await asyncio.to_thread(
            supabase_client.table("messages").select("count", count="exact").eq("user_id", user_id).execute
        )
        message_count = count_response.count if hasattr(count_response, 'count') else 0

        if message_count == 0:
            logger.debug(f"Нет сообщений для удаления у пользователя с ID {user_id}")
            return True

        # Удаляем все сообщения пользователя
        response = await asyncio.to_thread(
            supabase_client.table("messages").delete().eq("user_id", user_id).execute
        )

        logger.debug(f"Удалено {len(response.data) if response.data else 0} сообщений")
        return True
    except Exception as e:
        logger.error(f"Ошибка при удалении сообщений пользователя {user_id}: {str(e)}")
        return False