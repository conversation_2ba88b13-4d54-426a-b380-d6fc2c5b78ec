from aiogram import Router

# Импортируем роутер из модуля image_generation
from app.handlers.image_generation import router as image_router
# Импортируем роутер из модуля profile
from app.handlers.profile import router as profile_router
# Импортируем роутер из модуля voice
from app.handlers.voice import router as voice_router
# Импортируем роутер из модуля vision
from app.handlers.vision import router as vision_router
# Импортируем роутер из модуля referral
from app.handlers.referral import router as referral_router
# Импортируем роутер из модуля text_processing
from app.handlers.text_processing import router as text_router
# Импортируем роутер из модуля subscription
from app.handlers.subscription import router as subscription_router
# Импортируем роутер из модуля admin
from app.handlers.admin import router as admin_router
# Импортируем роутер из модуля base
from app.handlers.base import router as base_router

# Создаем главный роутер
router = Router()

# Подключаем базовый роутер (должен быть первым для обработки команды /start)
router.include_router(base_router)
# Подключаем роутер административных функций (команды администратора)
router.include_router(admin_router)
# Подключаем роутер системы подписок (команды подписок)
router.include_router(subscription_router)
# Подключаем роутер профиля пользователя (команды профиля)
router.include_router(profile_router)
# Подключаем роутер реферальной системы
router.include_router(referral_router)
# Подключаем роутер генерации изображений
router.include_router(image_router)
# Подключаем роутер обработки изображений
router.include_router(vision_router)
# Подключаем роутер голосовых сообщений
router.include_router(voice_router)
# Подключаем роутер обработки текстовых сообщений (должен быть последним)
router.include_router(text_router)

# В будущем здесь будут подключаться другие роутеры
