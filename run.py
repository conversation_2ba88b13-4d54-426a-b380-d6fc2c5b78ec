import os
import logging
import sys
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
from dotenv import load_dotenv
import asyncio
from aiogram import Bo<PERSON>, Dispatcher
from colorama import Fore, Style, init as colorama_init
from aiogram.client.default import DefaultBotProperties
from app.handlers import router
from app.supabase.client import supabase_client
from app.cache.user_cache import UserCache
from app.mcp_client.manager import MCPClientManager
from typing import Optional

# Создаем директорию для логов, если она не существует
os.makedirs('logs', exist_ok=True)

# Настройка логирования (остается без изменений)
# ... (весь код настройки логирования как был) ...
root_logger = logging.getLogger()
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)
current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
log_file_name = f"logs/bot_{current_time}.log"
file_handler = TimedRotatingFileHandler(log_file_name, when="midnight", interval=1, backupCount=30)
file_handler.setLevel(logging.INFO)
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)
LOG_COLORS = {logging.DEBUG: Fore.CYAN, logging.INFO: Fore.GREEN, logging.WARNING: Fore.YELLOW, logging.ERROR: Fore.RED, logging.CRITICAL: Fore.MAGENTA}
class ColoredFormatter(logging.Formatter):
    def format(self, record):
        log_color = LOG_COLORS.get(record.levelno, Fore.WHITE)
        record.levelname = f"{log_color}{record.levelname}{Style.RESET_ALL}"
        record.asctime = f"{Fore.BLUE}{self.formatTime(record, self.datefmt)}{Style.RESET_ALL}"
        message = record.getMessage()
        formatted_message = super().format(record)
        standard_prefix = f"{record.asctime} - {record.levelname} - "
        final_message = standard_prefix + message
        return final_message
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_formatter = ColoredFormatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
console_handler.setFormatter(console_formatter)
root_logger.setLevel(logging.INFO)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)
logger = logging.getLogger(__name__)
colorama_init(autoreset=True)
# --- Глобальная переменная удалена ---

# Основная функция запуска бота
async def main():
    # --- global mcp_manager удален ---

    load_dotenv()
    logger.info("=== ЗАПУСК БОТА ===")
    logger.info("Запуск бота...")
    logger.info(f"Логи сохраняются в файл: {log_file_name}")
    logger.info("Проверка подключения к Supabase...")

    cache_ttl = int(os.getenv("USER_CACHE_TTL", "300"))
    user_cache = UserCache(ttl=cache_ttl)
    logger.info(f"Инициализирован кеш пользователей с TTL={cache_ttl} секунд")

    try:
        supabase_url = os.environ.get("SUPABASE_URL")
        supabase_key = os.environ.get("SUPABASE_ANON_KEY")
        logger.info(f"SUPABASE_URL: {'Установлен' if supabase_url else 'Не установлен'}")
        logger.info(f"SUPABASE_ANON_KEY: {'Установлен' if supabase_key else 'Не установлен'}")
        if not supabase_url or not supabase_key:
            error_msg = "Не найдены переменные окружения SUPABASE_URL или SUPABASE_ANON_KEY"
            logger.error(error_msg)
            raise ValueError(error_msg)
        logger.info("Выполняем запрос к таблице users...")
        response = await asyncio.to_thread(supabase_client.table("users").select("count", count="exact").limit(1).execute)
        user_count = response.count if hasattr(response, 'count') else 0
        logger.info(f"Успешное подключение к Supabase. Количество пользователей: {user_count}")
        logger.info("Выполняем запрос к таблице messages...")
        messages_response = await asyncio.to_thread(supabase_client.table("messages").select("count", count="exact").limit(1).execute)
        message_count = messages_response.count if hasattr(messages_response, 'count') else 0
        logger.info(f"Количество сообщений в базе данных: {message_count}")
        logger.info("Все проверки подключения к Supabase успешно пройдены")
    except Exception as e:
        error_msg = f"Ошибка при подключении к Supabase: {e}"
        logger.error(error_msg)
        raise

    # --- Инициализируем менеджер ---
    mcp_manager = MCPClientManager()
    # -----------------------------

    logger.info("Инициализация бота Telegram...")
    bot_token = os.getenv("TG_TOKEN")
    if not bot_token:
        error_msg = "Не найдена переменная окружения TG_TOKEN"
        logger.error(error_msg)
        raise ValueError(error_msg)

    bot = Bot(token=bot_token, default=DefaultBotProperties(parse_mode="Markdown"))
    dp = Dispatcher()

    # --- Прикрепляем менеджер к Dispatcher ---
    dp['mcp_manager'] = mcp_manager
    # ---------------------------------------

    dp.include_router(router)

    # Запускаем MCP серверы и устанавливаем соединения
    await mcp_manager.startup()

    logger.info("Бот успешно запущен и готов к работе!")
    await dp.start_polling(bot)

if __name__ == "__main__":
    # Создаем временный dp для доступа к mcp_manager в finally
    # Это немного костыльно, но нужно для корректного shutdown при ошибках *до* старта polling
    temp_dp_for_finally = Dispatcher()
    try:
        logger.info("Старт приложения")
        # Передаем dp в asyncio.run, чтобы он был доступен в finally
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Бот выключен пользователем")
        # Получаем dp из main, если он был успешно создан
        # Это может не сработать, если ошибка произошла до инициализации dp в main
        # Поэтому используем temp_dp_for_finally как запасной вариант
        dp_instance = locals().get('dp', temp_dp_for_finally)

    except Exception as e:
        error_msg = f"Критическая ошибка: {e}"
        logger.error(error_msg)
        logger.error("", exc_info=True)
        dp_instance = locals().get('dp', temp_dp_for_finally)
    finally:
        # --- Получаем менеджер из dp ---
        # Используем dp_instance, который был определен в try/except
        mcp_manager_instance = dp_instance.get('mcp_manager') if 'dp_instance' in locals() else None
        if mcp_manager_instance:
             try:
                 asyncio.run(mcp_manager_instance.shutdown())
             except RuntimeError as e:
                 if "cannot call run() while another loop is running" in str(e) or \
                    "Cannot run the event loop while another loop is running" in str(e):
                     logger.warning("Не удалось запустить shutdown для MCP менеджера: цикл событий уже остановлен.")
                 else:
                     logger.error(f"Ошибка при запуске shutdown для MCP менеджера: {e}")
             except Exception as e:
                 logger.error(f"Непредвиденная ошибка при shutdown MCP менеджера: {e}")
        # -------------------------------

        logger.info("Завершение работы приложения")