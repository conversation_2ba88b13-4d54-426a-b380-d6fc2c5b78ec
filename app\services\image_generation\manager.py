# app/services/image_generation/manager.py
import importlib
import logging
from typing import Dict, Any

from app.services.image_generation.base import ImageGenerator
from config.image_generation_config import IMAGE_MODELS_CONFIG, PROVIDER_CLASSES

logger = logging.getLogger(__name__)

# Простой кеш для хранения уже созданных экземпляров генераторов
_generators_cache: Dict[str, ImageGenerator] = {}

def get_generator(model_id: str) -> ImageGenerator:
    """
    Фабричная функция. Получает инстанс генератора для указанной модели.
    Использует кеширование для предотвращения повторного создания объектов.
    """
    if model_id in _generators_cache:
        return _generators_cache[model_id]

    model_config = IMAGE_MODELS_CONFIG.get(model_id)
    if not model_config:
        raise ValueError(f"Конфигурация для модели '{model_id}' не найдена.")

    provider_name = model_config.get("provider")
    if not provider_name:
        raise ValueError(f"В конфигурации модели '{model_id}' не указан провайдер.")

    provider_path = PROVIDER_CLASSES.get(provider_name)
    if not provider_path:
        raise ValueError(f"Провайдер '{provider_name}' не зарегистрирован в PROVIDER_CLASSES.")

    try:
        # Динамически импортируем класс провайдера по его пути
        module_path, class_name = provider_path.rsplit('.', 1)
        module = importlib.import_module(module_path)
        GeneratorClass = getattr(module, class_name)
    except (ImportError, AttributeError) as e:
        logger.exception(f"Не удалось загрузить класс провайдера '{provider_path}'.")
        raise ImportError(f"Класс провайдера '{provider_path}' не найден.") from e

    # Создаем экземпляр, передавая ему его личную конфигурацию
    instance = GeneratorClass(config=model_config.get("params", {}))
    _generators_cache[model_id] = instance
    logger.info(f"Создан и закеширован генератор для модели '{model_id}' (провайдер: {provider_name}).")
    
    return instance
