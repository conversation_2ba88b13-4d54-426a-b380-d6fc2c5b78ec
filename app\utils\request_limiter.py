"""
Модуль для проверки лимитов запросов пользователей.
Содержит функции для проверки лимитов и получения пользователей из базы данных.
"""
from typing import Optional, Tuple
from aiogram.types import Message
from app.subscription_manager import SubscriptionManager
from app.supabase.users import get_user_by_tg_id, get_user_language
from app.localization import get_text


async def check_limits(message: Message, request_type: str) -> Tuple[bool, Optional[dict]]:
    """
    Проверяет лимиты запросов пользователя и отправляет соответствующие сообщения.

    Args:
        message: Объект сообщения Telegram
        request_type: Тип запроса ('text' или 'image')

    Returns:
        tuple: (можно_продолжать, объект_пользователя)
            - можно_продолжать (bool): True, если обработку можно продолжать
            - объект_пользователя (dict): Данные пользователя из базы или None, если проверка не пройдена
    """
    # Проверяем лимиты запросов
    can_make_request, limit_message = await SubscriptionManager.can_make_request(message.from_user.id, request_type)
    if not can_make_request:
        await message.answer(limit_message)
        return False, None

    # Если получили сообщение и разрешение на обработку, но limit_message не пустое,
    # значит это информационное сообщение (например, об истечении подписки)
    if can_make_request and limit_message:
        await message.answer(limit_message)

    # Получаем пользователя из Supabase
    user = await get_user_by_tg_id(message.from_user.id)
    if not user:
        user_language = await get_user_language(message.from_user.id)
        error_message = get_text("errors", "user_not_found", user_language)
        await message.answer(error_message)
        return False, None

    return True, user
