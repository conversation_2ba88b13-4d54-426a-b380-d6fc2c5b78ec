"""
Модуль для кеширования данных пользователей.
Реализует механизмы кеширования и инвалидации кеша для данных пользователей из Supabase.
"""

import logging
import time
from typing import Dict, Any, Optional, Callable
from functools import wraps

# Настройка логирования
logger = logging.getLogger(__name__)

class UserCache:
    """
    Класс для кеширования данных пользователей.
    Реализует паттерн Singleton для обеспечения единственного экземпляра кеша.
    """
    _instance = None
    _cache = {}  # Кеш пользователей: {tg_id: (данные, timestamp)}
    _ttl = 300  # Время жизни кеша в секундах (5 минут по умолчанию)
    
    def __new__(cls, ttl: int = 300):
        if cls._instance is None:
            cls._instance = super(UserCache, cls).__new__(cls)
            cls._instance._ttl = ttl
            logger.info(f"Инициализирован кеш пользователей с TTL={ttl} секунд")
        return cls._instance
    
    def get(self, tg_id: int) -> Optional[Dict[str, Any]]:
        """
        Получает данные пользователя из кеша.
        
        Args:
            tg_id: ID пользователя в Telegram
            
        Returns:
            Optional[Dict[str, Any]]: Данные пользователя или None, если пользователь не найден или кеш устарел
        """
        if tg_id in self._cache:
            data, timestamp = self._cache[tg_id]
            # Проверяем, не устарел ли кеш
            if time.time() - timestamp < self._ttl:
                logger.debug(f"Данные пользователя {tg_id} получены из кеша")
                return data
            else:
                # Удаляем устаревшие данные
                logger.debug(f"Данные пользователя {tg_id} в кеше устарели")
                del self._cache[tg_id]
        return None
    
    def set(self, tg_id: int, data: Dict[str, Any]) -> None:
        """
        Сохраняет данные пользователя в кеш.
        
        Args:
            tg_id: ID пользователя в Telegram
            data: Данные пользователя
        """
        self._cache[tg_id] = (data, time.time())
        logger.debug(f"Данные пользователя {tg_id} сохранены в кеш")
    
    def invalidate(self, tg_id: int) -> None:
        """
        Инвалидирует (удаляет) данные пользователя из кеша.
        
        Args:
            tg_id: ID пользователя в Telegram
        """
        if tg_id in self._cache:
            del self._cache[tg_id]
            logger.debug(f"Данные пользователя {tg_id} удалены из кеша")
    
    def clear(self) -> None:
        """
        Очищает весь кеш.
        """
        self._cache.clear()
        logger.debug("Кеш пользователей очищен")
    
    @property
    def size(self) -> int:
        """
        Возвращает размер кеша (количество пользователей).
        
        Returns:
            int: Количество пользователей в кеше
        """
        return len(self._cache)
    
    @property
    def ttl(self) -> int:
        """
        Возвращает время жизни кеша.
        
        Returns:
            int: Время жизни кеша в секундах
        """
        return self._ttl
    
    @ttl.setter
    def ttl(self, value: int) -> None:
        """
        Устанавливает время жизни кеша.
        
        Args:
            value: Время жизни кеша в секундах
        """
        self._ttl = value
        logger.info(f"Установлено новое время жизни кеша: {value} секунд")

def cached_user(func: Callable) -> Callable:
    """
    Декоратор для кеширования результатов функций, работающих с пользователями.
    
    Args:
        func: Функция для декорирования
        
    Returns:
        Callable: Декорированная функция
    """
    @wraps(func)
    async def wrapper(tg_id: int, *args, **kwargs):
        # Получаем экземпляр кеша
        cache = UserCache()
        
        # Пытаемся получить данные из кеша
        cached_data = cache.get(tg_id)
        if cached_data is not None:
            return cached_data
        
        # Если данных нет в кеше, вызываем оригинальную функцию
        result = await func(tg_id, *args, **kwargs)
        
        # Если результат не None, сохраняем его в кеш
        if result is not None:
            cache.set(tg_id, result)
        
        return result
    
    return wrapper
