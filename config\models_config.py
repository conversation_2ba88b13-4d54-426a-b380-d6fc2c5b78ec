from typing import Dict, Any, Optional, TypedDict
import os
from dotenv import load_dotenv

# Загрузка переменных окружения
load_dotenv()

# Определение структуры конфигурации модели для улучшения типизации
class ModelConfig(TypedDict):
    name: str
    max_tokens: int
    temperature: float
    tools: bool
    vision: bool
    base_url: str
    api_key: Optional[str]
    
# Получение API ключа с проверкой
def get_api_key() -> Optional[str]:
    api_key = os.getenv("LITELLM_API_KEY")
    if not api_key:
        # Просто возвращаем None вместо вызова исключения, чтобы позволить
        # инициализировать конфигурацию даже без ключа
        return None
    return api_key

# Базовый URL для всех моделей
BASE_API_URL = "http://193.233.114.29:4000/v1"

# Конфигурация моделей с группировкой по типам
MODELS_CONFIG: Dict[str, ModelConfig] = {
    # OpenAI модели
    "gpt-4.1": {
        "name": "GPT-4.1",
        "max_tokens": 4096,
        "temperature": 0.7,
        "tools": True,
        "vision": True,
        "base_url": "http://193.233.114.29:4000/v1",
        "api_key": get_api_key()
    },
    "gpt-4.1-nano": {
        "name": "GPT-4.1-nano",
        "max_tokens": 4096,
        "temperature": 0.7,
        "tools": True,
        "vision": True,
        "base_url": "http://193.233.114.29:4000/v1",
        "api_key": get_api_key()
    },

    # Новая модель GPT-5-nano с тем же провайдером
    "gpt-5-nano": {
        "name": "GPT-5-nano",
        "max_tokens": 4096,
        "temperature": 1,
        "tools": True,
        "vision": True,
        "base_url": "http://193.233.114.29:4000/v1",
        "api_key": get_api_key()
    },
    
    # Специализированные модели
    "elixposearch": {
        "name": "SearchGPT",
        "max_tokens": 4096,
        "temperature": 0.7,
        "tools": True,
        "vision": True,
        "base_url": "http://193.233.114.29:4000/v1",
        "api_key": get_api_key()
    },
    "gemini-2.5-flash-preview-05-20": {
        "name": "Gemini 2.5 Pro",
        "max_tokens": 4096,
        "temperature": 1.0,
        "tools": True,
        "vision": True,
        "base_url": "http://193.233.114.29:4000/v1",
        "api_key": get_api_key()
    },
}

# Модель по умолчанию
DEFAULT_MODEL = "gpt-4.1"

# Функция для получения конфигурации модели с проверкой
def get_model_config(model_id: str) -> ModelConfig:
    """
    Получение конфигурации модели по её идентификатору.
    Если модель не найдена, возвращает конфигурацию модели по умолчанию.
    
    Args:
        model_id: Идентификатор модели
        
    Returns:
        Конфигурация запрошенной модели или модели по умолчанию
    """
    return MODELS_CONFIG.get(model_id, MODELS_CONFIG[DEFAULT_MODEL])