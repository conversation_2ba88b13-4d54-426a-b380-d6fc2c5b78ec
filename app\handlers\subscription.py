import logging
from aiogram import Router
from aiogram.types import Message, CallbackQuery, LabeledPrice, PreCheckoutQuery
from app.localization import get_text
from app.keyboards import get_subscription_plans_keyboard
from config.subscription_config import SUBSCRIPTION_PLANS
from app.supabase.users import get_user_by_tg_id, update_user_subscription, get_user_language
from datetime import datetime, timedelta, timezone
from app.handlers.profile import send_or_edit_profile_message

router = Router()
logger = logging.getLogger(__name__)

@router.callback_query(lambda c: c.data == "show_subscription_plans")
async def show_subscription_plans(callback_query: CallbackQuery):
    user_language = await get_user_language(callback_query.from_user.id)
    plans_info = ""

    emoji_map = {
        "free": "🎁",
        "standard": "⭐️",
        "premium": "👑"
    }

    for plan_id, plan in SUBSCRIPTION_PLANS.items():
        # Форматируем лимиты с учетом бесконечности
        text_limit = "∞" if plan['text_requests_per_day'] == float('inf') else plan['text_requests_per_day']
        image_limit = "∞" if plan['image_requests_per_day'] == float('inf') else plan['image_requests_per_day']

        # Определяем продолжительность
        duration = get_text("subscription", "duration_unlimited", user_language) if plan_id == 'free' else get_text("subscription", "duration_days", user_language)

        # Добавляем разделитель между планами (кроме первого)
        if plan_id != "free":
            plans_info += get_text("subscription", "plan_separator", user_language)

        # Используем локализованный шаблон для каждого плана
        plan_info = get_text("subscription", "plan_info", user_language).format(
            emoji=emoji_map[plan_id],
            plan_name=plan['name'].upper(),
            text_limit=text_limit,
            image_limit=image_limit,
            price=plan['price'],
            duration=duration
        )

        plans_info += plan_info

    await callback_query.message.edit_text(
        get_text("subscription", "subscription_plans", user_language).format(plans_info=plans_info),
        parse_mode="Markdown",
        reply_markup=get_subscription_plans_keyboard(user_language)
    )


@router.callback_query(lambda c: c.data.startswith("buy_plan_"))
async def process_buy_plan(callback_query: CallbackQuery):
    plan_id = callback_query.data.split('_')[2]
    plan = SUBSCRIPTION_PLANS[plan_id]

    # Проверяем текущую подписку пользователя
    user = await get_user_by_tg_id(callback_query.from_user.id)
    if user:
        current_plan = SUBSCRIPTION_PLANS[user["subscription_plan"]]

        # Если пытается купить текущую подписку
        if user["subscription_plan"] == plan_id:
            user_language = await get_user_language(callback_query.from_user.id)
            await callback_query.answer(
                get_text("subscription", "already_active", user_language).format(plan_name=current_plan['name']),
                show_alert=True
            )
            return

        # Если пытается купить подписку ниже уровнем
        if (current_plan['price'] > plan['price'] and plan_id != "free"):
            user_language = await get_user_language(callback_query.from_user.id)
            await callback_query.answer(
                get_text("subscription", "higher_plan_exists", user_language).format(current_plan=current_plan['name']),
                show_alert=True
            )
            return

    prices = [LabeledPrice(label=plan['name'], amount=plan['price'])]

    user_language = await get_user_language(callback_query.from_user.id)
    invoice_description = get_text("subscription", "invoice_description", user_language).format(
        plan_name=plan['name'],
        description=plan['description']
    )

    await callback_query.message.answer_invoice(
        title=f"Подписка {plan['name']}",
        description=invoice_description,
        provider_token="",  # Для Telegram Stars оставляем пустым
        currency="XTR",
        prices=prices,
        payload=plan_id,
        start_parameter=f"subscribe_{plan_id}"
    )


# Обработчик предварительной проверки платежа
@router.pre_checkout_query()
async def pre_checkout_handler(pre_checkout_query: PreCheckoutQuery):
    await pre_checkout_query.answer(ok=True)


# Обработчик успешной оплаты
@router.message(lambda message: message.successful_payment)
async def successful_payment_handler(message: Message):
    plan_id = message.successful_payment.invoice_payload
    plan = SUBSCRIPTION_PLANS[plan_id]

    # Получаем пользователя из Supabase
    user = await get_user_by_tg_id(message.from_user.id)
    if user:
        # Устанавливаем дату окончания подписки (30 дней) с учетом временной зоны
        subscription_end_date = datetime.now(timezone(timedelta(hours=3))) + timedelta(days=30)

        # Обновляем данные пользователя
        await update_user_subscription(
            user["id"],
            plan_id,
            subscription_end_date.isoformat()
        )

        # Форматируем лимиты с учетом бесконечности
        text_limit = "∞" if plan['text_requests_per_day'] == float('inf') else plan['text_requests_per_day']
        image_limit = "∞" if plan['image_requests_per_day'] == float('inf') else plan['image_requests_per_day']

        # Форматируем дату окончания
        end_date = subscription_end_date.strftime("%d.%m.%Y")

        user_language = await get_user_language(message.from_user.id)
        success_message = get_text("success", "subscription_activated", user_language).format(
            plan_name=plan['name'],
            text_limit=text_limit,
            image_limit=image_limit,
            end_date=end_date
        )

        # Показываем краткое уведомление об успешной активации
        await message.answer(success_message, parse_mode="Markdown")

        # Отображаем обновленный профиль
        await send_or_edit_profile_message(message, message.from_user.id, message.bot, edit=False)