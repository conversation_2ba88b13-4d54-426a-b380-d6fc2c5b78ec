import logging
from aiogram import Router
from aiogram.types import Message, CallbackQuery
from aiogram.filters import CommandStart, CommandObject
from aiogram.fsm.context import FSMContext
from app.localization import get_text
import app.keyboards as kb
from app.supabase.users import set_user, set_user_language, get_user_language
from app.handlers.profile import send_or_edit_profile_message

router = Router()
logger = logging.getLogger(__name__)

@router.message(CommandStart())
async def cmd_start(message: Message, state: FSMContext, command: CommandObject):
    referral_code = command.args
    logger.info(f"User {message.from_user.id} started bot. Referral code: {referral_code if referral_code else 'None'}")

    # Создаем пользователя с языком по умолчанию (ru)
    await set_user(message.from_user.id, referral_code)

    # Предлагаем выбрать язык
    language_message = get_text("common", "language_selection")

    # Флаг генерации изображения теперь хранится в памяти и сбрасывается автоматически при перезапуске бота

    # Отправляем сообщение с выбором языка
    await message.answer(language_message, reply_markup=kb.language_start_kb)
    await state.clear()

@router.callback_query(lambda c: c.data.startswith("start_language:"))
async def process_start_language(callback_query: CallbackQuery):
    # Получаем выбранный язык
    language = callback_query.data.split(":")[1]
    user_id = callback_query.from_user.id

    # Устанавливаем язык пользователя
    await set_user_language(user_id, language)

    # Получаем приветственное сообщение на выбранном языке
    user_language = await get_user_language(user_id)
    start_message = get_text("common", "start", user_language)
    start_message = start_message.format(
        name=callback_query.from_user.first_name
    )

    # Отправляем приветственное сообщение
    await callback_query.message.edit_text(start_message, parse_mode="Markdown")


    # Отвечаем на коллбэк, чтобы убрать часы
    await callback_query.answer()

# Обработчик кнопки выбора языка перенесен в профиль пользователя

@router.callback_query(lambda c: c.data.startswith("language:"))
async def process_language_change(callback_query: CallbackQuery):
    # Получаем выбранный язык
    language = callback_query.data.split(":")[1]
    user_id = callback_query.from_user.id

    # Устанавливаем язык пользователя
    await set_user_language(user_id, language)

    # Получаем сообщение об успешной смене языка для всплывающего уведомления
    user_language = await get_user_language(user_id)
    language_message = get_text("common", "language", user_language)
    if isinstance(language_message, dict):
        language_message = language_message["selected"]

    # Показываем всплывающее уведомление
    await callback_query.answer(language_message, show_alert=False)

    # Отображаем обновленный профиль с новым языком
    await send_or_edit_profile_message(callback_query.message, user_id, callback_query.bot, edit=True)