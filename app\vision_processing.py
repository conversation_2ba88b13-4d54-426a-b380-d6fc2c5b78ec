import logging
from typing import Dict, Any, List, Union, Optional

# Настройка логирования
logger = logging.getLogger(__name__)

def process_vision_content(message: Dict[str, Any], supports_vision: bool) -> Optional[Union[str, List[Dict[str, Any]]]]:
    """
    Обрабатывает содержимое сообщения с учетом наличия изображения и поддержки vision.

    Args:
        message: Сообщение из истории чата
        supports_vision: Поддерживает ли модель работу с изображениями

    Returns:
        Обработанное содержимое сообщения или None, если сообщение следует пропустить
    """
    # Если у сообщения есть изображение и модель не поддерживает vision,
    # пропускаем это сообщение
    if message["image_data"] and not supports_vision:
        return None

    # Формируем контент сообщения в зависимости от наличия изображения
    if message["image_data"] and supports_vision:
        return [
            {"type": "text", "text": message["caption"] or ""},
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{message['image_data']}",
                    "detail": "high"
                }
            }
        ]
    else:
        return message["content"]

def prepare_chat_history(messages: List[Dict[str, Any]], system_prompt: str,
                         supports_vision: bool, question: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Подготавливает историю чата для отправки в LLM с учетом vision.

    Args:
        messages: История сообщений пользователя
        system_prompt: Системный промпт для модели
        supports_vision: Поддерживает ли модель работу с изображениями
        question: Текущий вопрос пользователя

    Returns:
        Подготовленная история чата для отправки в модель
    """
    # Формирование списка сообщений для запроса
    chat_history = [{"role": "system", "content": system_prompt}]

    for message in messages:
        content = process_vision_content(message, supports_vision)
        if content is None:
            continue

        chat_history.append({"role": message["role"], "content": content})

    # Добавление текущего вопроса, если он есть и не дублирует последнее сообщение
    if question and (not messages or messages[-1]["content"] != question):
        chat_history.append({"role": "user", "content": str(question)})

    return chat_history