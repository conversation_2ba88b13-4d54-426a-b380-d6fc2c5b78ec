import os
import logging
import importlib
from typing import Dict, Any, Optional
from functools import lru_cache

logger = logging.getLogger(__name__)

class Localization:
    """
    Класс для управления локализацией текстов в приложении.
    Поддерживает загрузку текстов на разных языках и кеширование для оптимизации.
    """
    _instance = None
    _default_language = "ru"
    _supported_languages = ["ru", "en"]
    _text_categories = [
        "common", "errors", "success", "menu", "profile",
        "subscription", "image_generation", "text_processing",
        "vision", "voice", "referral", "newsletter", "edit_image"
    ]
    _texts_cache = {}  # Кеш для текстов

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Localization, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """Инициализация локализации"""
        # Предварительная загрузка текстов для всех языков
        for lang in self._supported_languages:
            self._load_texts(lang)

    @lru_cache(maxsize=32)
    def _load_texts(self, language: str) -> Dict[str, Any]:
        """
        Загружает тексты для указанного языка.
        
        Args:
            language: Код языка (ru, en)
            
        Returns:
            Dict[str, Any]: Словарь с текстами
        """
        if language not in self._supported_languages:
            logger.warning(f"Язык {language} не поддерживается. Используется язык по умолчанию: {self._default_language}")
            language = self._default_language
            
        texts = {}
        
        # Загружаем тексты из всех категорий
        for category in self._text_categories:
            try:
                module_path = f"app.localization.{language}.{category}"
                module = importlib.import_module(module_path)
                
                if hasattr(module, "TEXTS"):
                    texts[category] = module.TEXTS
                else:
                    logger.error(f"В модуле {module_path} отсутствует переменная TEXTS")
            except ImportError as e:
                logger.error(f"Ошибка при импорте модуля {module_path}: {e}")
                
        self._texts_cache[language] = texts
        return texts

    def get_text(self, category: str, key: str, language: str = None) -> Any:
        """
        Получает текст по категории и ключу.
        
        Args:
            category: Категория текста (errors, success, menu и т.д.)
            key: Ключ текста
            language: Код языка (ru, en). Если None, используется язык по умолчанию
            
        Returns:
            Any: Текст или словарь с текстами
        """
        if language is None:
            language = self._default_language
            
        if language not in self._supported_languages:
            language = self._default_language
            
        # Загружаем тексты, если их нет в кеше
        if language not in self._texts_cache:
            self._load_texts(language)
            
        texts = self._texts_cache.get(language, {})
        category_texts = texts.get(category, {})
        
        if key in category_texts:
            return category_texts[key]
        else:
            # Если текст не найден, пробуем найти его в языке по умолчанию
            if language != self._default_language:
                logger.warning(f"Текст {category}.{key} не найден для языка {language}. Используется язык по умолчанию.")
                return self.get_text(category, key, self._default_language)
            else:
                logger.error(f"Текст {category}.{key} не найден для языка по умолчанию.")
                return f"[Missing text: {category}.{key}]"

    def set_default_language(self, language: str):
        """
        Устанавливает язык по умолчанию.
        
        Args:
            language: Код языка (ru, en)
        """
        if language in self._supported_languages:
            self._default_language = language
        else:
            logger.warning(f"Язык {language} не поддерживается. Язык по умолчанию не изменен.")

    def get_supported_languages(self):
        """
        Возвращает список поддерживаемых языков.
        
        Returns:
            list: Список кодов языков
        """
        return self._supported_languages.copy()

# Создаем глобальный экземпляр класса локализации
_localization = Localization()

# Функция для получения текста
def get_text(category: str, key: str, language: str = None) -> Any:
    """
    Получает текст по категории и ключу.
    
    Args:
        category: Категория текста (errors, success, menu и т.д.)
        key: Ключ текста
        language: Код языка (ru, en). Если None, используется язык по умолчанию
        
    Returns:
        Any: Текст или словарь с текстами
    """
    return _localization.get_text(category, key, language)

# Функция для установки языка по умолчанию
def set_default_language(language: str):
    """
    Устанавливает язык по умолчанию.
    
    Args:
        language: Код языка (ru, en)
    """
    _localization.set_default_language(language)

# Функция для получения списка поддерживаемых языков
def get_supported_languages():
    """
    Возвращает список поддерживаемых языков.
    
    Returns:
        list: Список кодов языков
    """
    return _localization.get_supported_languages()
