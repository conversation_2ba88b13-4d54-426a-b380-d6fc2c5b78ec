import logging
from aiogram import Router, Bot
from aiogram.types import Message, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.filters import Command

import app.keyboards as kb
from app.localization import get_text
from app.supabase.users import (
    set_user_model, get_user_model, set_user_role, get_user_role,
    get_user_referral_count, get_user_referral_code, get_user_by_tg_id,
    set_user_language, get_user_language, get_user_image_model, set_user_image_model
)
from config.models_config import MODELS_CONFIG
from config.subscription_config import SUBSCRIPTION_PLANS
from config.roles_config import ROLES_CONFIG, DEFAULT_ROLE_ID
from config.image_generation_config import IMAGE_MODELS_CONFIG
from datetime import datetime, timedelta, timezone
from app.utils.error_handler import handle_error

router = Router()
logger = logging.getLogger(__name__)

async def send_or_edit_profile_message(message: Message, user_id: int, bot: Bot, edit: bool = False):
    """Отправляет или редактирует сообщение с профилем пользователя.

    Args:
        message: Сообщение для ответа или редактирования
        user_id: ID пользователя в Telegram
        bot: Экземпляр бота
        edit: Флаг, указывающий нужно ли редактировать сообщение (True) или отправлять новое (False)
    """
    user_model = await get_user_model(user_id)
    model_name = MODELS_CONFIG[user_model]["name"] if user_model in MODELS_CONFIG else user_model

    # Получаем модель изображений пользователя
    user_image_model = await get_user_image_model(user_id)
    image_model_name = IMAGE_MODELS_CONFIG[user_image_model]["name"] if user_image_model in IMAGE_MODELS_CONFIG else user_image_model

    user_role_id = await get_user_role(user_id)

    # Получаем язык пользователя для локализации роли
    user_language = await get_user_language(user_id)

    # Получаем локализованное имя роли
    role_display_name = kb.get_localized_role_name(user_role_id, user_language)

    referral_count = await get_user_referral_count(user_id)
    referral_code = await get_user_referral_code(user_id)
    bot_username = (await bot.get_me()).username

    referral_link = f"https://t.me/{bot_username}?start={referral_code}"

    # Получаем пользователя из Supabase
    user = await get_user_by_tg_id(user_id)
    if not user:
        await handle_error(ValueError(f"Пользователь с ID {user_id} не найден"),
                          message, "errors.user_not_found", user_id=user_id)
        return

    plan = SUBSCRIPTION_PLANS[user["subscription_plan"]]

    # Форматируем лимиты с учетом бесконечности
    text_limit = "∞" if plan['text_requests_per_day'] == float('inf') else plan['text_requests_per_day']
    image_limit = "∞" if plan['image_requests_per_day'] == float('inf') else plan['image_requests_per_day']

    # Добавляем информацию о сроке действия подписки
    subscription_info = ""
    if user["subscription_end_date"] and user["subscription_plan"] != "free":
        now = datetime.now(timezone(timedelta(hours=3)))  # Московское время

        # Преобразуем строку даты в объект datetime
        subscription_end_date = datetime.fromisoformat(user["subscription_end_date"].replace('Z', '+00:00'))

        # Убедимся, что subscription_end_date имеет временную зону
        if subscription_end_date.tzinfo is None:
            subscription_end_date = subscription_end_date.replace(
                tzinfo=timezone(timedelta(hours=3))
            )

        days_left = (subscription_end_date - now).days
        time_left = f"{days_left} дн." if days_left > 0 else f"{((subscription_end_date - now).seconds // 3600)} ч."
        end_date = subscription_end_date.strftime("%d.%m.%Y")
        subscription_info_template = get_text("subscription", "subscription_info", user_language)
        subscription_info = subscription_info_template.format(
            time_left=time_left,
            end_date=end_date
        )
    profile_text = get_text("profile", "user_profile", user_language).format(
        model_name=model_name,
        image_model_name=image_model_name,
        role_display_name=role_display_name,
        plan_name=plan['name'],
        subscription_info=subscription_info,
        text_requests=user["text_requests_today"],
        text_limit=text_limit,
        image_requests=user["image_requests_today"],
        image_limit=image_limit,
        referral_link=referral_link,
        referral_count=referral_count
    )

    # Получаем клавиатуру настроек профиля
    keyboard = kb.get_profile_settings_keyboard(user_language)

    # В зависимости от флага edit редактируем сообщение или отправляем новое
    if edit:
        await message.edit_text(profile_text, reply_markup=keyboard, parse_mode="Markdown")
    else:
        await message.answer(profile_text, reply_markup=keyboard, parse_mode="Markdown")

@router.message(Command("profile"))
async def my_profile(message: Message, from_callback: bool = False):
    user_id = message.from_user.id if not from_callback else message.chat.id
    await send_or_edit_profile_message(message, user_id, message.bot, edit=False)


@router.callback_query(lambda c: c.data == "back_to_menu")
async def back_to_menu(callback_query: CallbackQuery):
    user_language = await get_user_language(callback_query.from_user.id)
    select_option_message = get_text("menu", "select_option", user_language)
    await callback_query.message.answer(select_option_message)
    await callback_query.answer()


@router.callback_query(lambda c: c.data == "change_model")
async def change_model(callback_query: CallbackQuery):
    user_language = await get_user_language(callback_query.from_user.id)
    select_model_message = get_text("menu", "select_model", user_language)

    # Получаем текущую модель пользователя
    current_model = await get_user_model(callback_query.from_user.id)

    # Создаем динамическую клавиатуру
    builder = InlineKeyboardBuilder()

    # Добавляем кнопки моделей с галочкой для текущей
    for model_id, model_info in MODELS_CONFIG.items():
        button_text = f"✅ {model_info['name']}" if model_id == current_model else model_info['name']
        builder.add(InlineKeyboardButton(text=button_text, callback_data=f"model_{model_id}"))

    # Добавляем локализованную кнопку "Назад"
    builder.add(kb.get_back_button(user_language, "back_to_profile"))

    # Настраиваем компоновку (по одной кнопке в строке)
    builder.adjust(1)

    await callback_query.message.edit_text(
        select_model_message,
        reply_markup=builder.as_markup(),
        parse_mode="Markdown"
    )
    await callback_query.answer()


@router.callback_query(lambda c: c.data == "info_models")
async def info_models(callback_query: CallbackQuery):
    user_language = await get_user_language(callback_query.from_user.id)
    models_info_message = get_text("menu", "models_info", user_language)
    await callback_query.message.answer(models_info_message)
    await callback_query.answer()


@router.callback_query(lambda c: c.data == "change_role")
async def change_role(callback_query: CallbackQuery):
    user_language = await get_user_language(callback_query.from_user.id)

    # Получаем текущую роль пользователя
    current_role_id = await get_user_role(callback_query.from_user.id)

    # Создаем динамическую клавиатуру
    builder = InlineKeyboardBuilder()

    # Добавляем кнопки ролей с галочкой для текущей
    for role_id, role_info in ROLES_CONFIG.items():
        localized_name = kb.get_localized_role_name(role_id, user_language)
        button_text = f"✅ {localized_name}" if role_id == current_role_id else localized_name
        builder.add(InlineKeyboardButton(text=button_text, callback_data=f"role_{role_id}"))

    # Добавляем локализованную кнопку "Назад"
    builder.add(kb.get_back_button(user_language, "back_to_profile"))

    # Настраиваем компоновку (по одной кнопке в строке)
    builder.adjust(1)

    await callback_query.message.edit_text(
        get_text("menu", "select_role", user_language),
        reply_markup=builder.as_markup(),
        parse_mode="Markdown"
    )
    await callback_query.answer()


@router.callback_query(lambda c: c.data == "image_settings")
async def image_settings(callback_query: CallbackQuery):
    """Показывает настройки изображений"""
    user_language = await get_user_language(callback_query.from_user.id)

    # Получаем локализованные тексты
    buttons_texts = get_text("profile", "buttons", user_language)
    settings_text = get_text("profile", "titles", user_language)["image_settings"]

    # Создаем клавиатуру с кнопкой изменения модели изображений
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(
            text=buttons_texts.get("change_image_model", "🎨 Change Image Model"),
            callback_data="change_image_model"
        )],
        [kb.get_back_button(user_language, "back_to_profile")]
    ])

    await callback_query.message.edit_text(
        settings_text,
        reply_markup=keyboard,
        parse_mode="Markdown"
    )
    await callback_query.answer()


@router.callback_query(lambda c: c.data == "change_image_model")
async def change_image_model(callback_query: CallbackQuery):
    """Показывает меню выбора модели изображений"""
    user_language = await get_user_language(callback_query.from_user.id)
    select_image_model_message = get_text("menu", "select_image_model", user_language)

    # Получаем текущую модель изображений пользователя
    current_image_model = await get_user_image_model(callback_query.from_user.id)

    # Создаем динамическую клавиатуру
    builder = InlineKeyboardBuilder()

    # Добавляем кнопки моделей изображений с галочкой для текущей
    model_buttons = []
    for model_id, model_info in IMAGE_MODELS_CONFIG.items():
        button_text = f"✅ {model_info['name']}" if model_id == current_image_model else model_info['name']
        button = InlineKeyboardButton(text=button_text, callback_data=f"image_model_{model_id}")
        builder.add(button)
        model_buttons.append(button)

    # Создаем кнопки "Профиль" и "Назад"
    profile_button = kb.get_profile_button(user_language, "back_to_profile")
    back_button = kb.get_back_button(user_language, "image_settings")

    # Добавляем кнопки "Профиль" и "Назад" в одну строку
    builder.row(profile_button, back_button)

    # Настраиваем компоновку: каждая кнопка модели в отдельной строке, 
    # а последние две кнопки (Профиль и Назад) в одной строке
    adjust_params = [1] * len(model_buttons) + [2]
    builder.adjust(*adjust_params)

    await callback_query.message.edit_text(
        select_image_model_message,
        reply_markup=builder.as_markup(),
        parse_mode="Markdown"
    )
    await callback_query.answer()


@router.callback_query(lambda c: c.data and c.data.startswith('model_'))
async def process_model_selection(callback_query: CallbackQuery):
    model_id = callback_query.data.split('_')[1]
    if model_id in MODELS_CONFIG:
        try:
            await set_user_model(callback_query.from_user.id, model_id)
            # Добавляем локализованное всплывающее уведомление при выборе модели
            user_language = await get_user_language(callback_query.from_user.id)
            notification_text = get_text("profile", "notifications", user_language)["model_set"].format(
                model_name=MODELS_CONFIG[model_id]['name']
            )
            await callback_query.answer(notification_text, show_alert=False)
            # Отображаем обновленный профиль
            await send_or_edit_profile_message(callback_query.message, callback_query.from_user.id, callback_query.bot, edit=True)
        except Exception as e:
            # Используем функцию handle_error для обработки ошибки
            await handle_error(e, callback_query.message, "menu.model_error",
                              user_id=callback_query.from_user.id,
                              additional_info="Ошибка при установке модели")
            user_language = await get_user_language(callback_query.from_user.id)
            error_text = get_text("profile", "notifications", user_language)["model_error"]
            await callback_query.answer(error_text, show_alert=False)
    else:
        await callback_query.answer()


@router.callback_query(lambda c: c.data and c.data.startswith('image_model_'))
async def process_image_model_selection(callback_query: CallbackQuery):
    """Обрабатывает выбор модели изображений"""
    model_id = callback_query.data.split('_')[2]  # image_model_flux -> flux

    # Проверяем, что модель существует в конфигурации
    if model_id in IMAGE_MODELS_CONFIG:
        try:
            await set_user_image_model(callback_query.from_user.id, model_id)

            # Добавляем локализованное всплывающее уведомление при выборе модели
            user_language = await get_user_language(callback_query.from_user.id)
            model_name = IMAGE_MODELS_CONFIG[model_id]['name']
            notification_text = get_text("profile", "notifications", user_language)["image_model_set"].format(
                model_name=model_name
            )
            await callback_query.answer(notification_text, show_alert=False)

            # Возвращаемся к настройкам изображений
            await image_settings(callback_query)
        except Exception as e:
            # Используем функцию handle_error для обработки ошибки
            await handle_error(e, callback_query.message, "menu.model_error",
                              user_id=callback_query.from_user.id,
                              additional_info="Ошибка при установке модели изображений")
            user_language = await get_user_language(callback_query.from_user.id)
            error_text = get_text("profile", "notifications", user_language)["image_model_error"]
            await callback_query.answer(error_text, show_alert=False)
    else:
        await callback_query.answer()


@router.callback_query(lambda c: c.data and c.data.startswith('role_'))
async def process_role_selection(callback_query: CallbackQuery):
    role_id = callback_query.data.split('_')[1]
    await set_user_role(callback_query.from_user.id, role_id)

    # Получаем локализованное имя роли
    user_language = await get_user_language(callback_query.from_user.id)
    display_name = kb.get_localized_role_name(role_id, user_language)

    # Добавляем локализованное всплывающее уведомление при выборе роли
    notification_text = get_text("profile", "notifications", user_language)["role_set"].format(
        role_name=display_name
    )
    await callback_query.answer(notification_text, show_alert=False)

    # Отображаем обновленный профиль
    await send_or_edit_profile_message(callback_query.message, callback_query.from_user.id, callback_query.bot, edit=True)


@router.callback_query(lambda c: c.data == "back_to_profile")
async def back_to_profile(callback_query: CallbackQuery):
    await send_or_edit_profile_message(callback_query.message, callback_query.from_user.id, callback_query.bot, edit=True)
    await callback_query.answer()


@router.callback_query(lambda c: c.data == "show_privacy")
async def show_privacy(callback_query: CallbackQuery):
    """Показывает информацию о конфиденциальности"""
    user_language = await get_user_language(callback_query.from_user.id)
    privacy_text = get_text("common", "privacy", user_language)
    await callback_query.message.edit_text(
        privacy_text["title"] + "\n\n" + privacy_text["main"],
        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
            [kb.get_back_button(user_language, "back_to_profile")]
        ]),
        parse_mode="Markdown"
    )
    await callback_query.answer()


@router.callback_query(lambda c: c.data == "change_language")
async def change_language(callback_query: CallbackQuery):
    """Показывает меню выбора языка"""
    # Получаем сообщение с выбором языка
    user_language = await get_user_language(callback_query.from_user.id)
    language_message = get_text("common", "language", user_language)
    if isinstance(language_message, dict):
        language_message = language_message["select"]

    # Отправляем сообщение с выбором языка
    await callback_query.message.edit_text(
        language_message,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🇷🇺 Русский", callback_data="language:ru")],
            [InlineKeyboardButton(text="🇬🇧 English", callback_data="language:en")],
            [kb.get_back_button(user_language, "back_to_profile")]
        ])
    )
    await callback_query.answer()