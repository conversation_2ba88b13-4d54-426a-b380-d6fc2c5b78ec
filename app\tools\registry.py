"""
Диспетчер инструментов (Tool Registry).

Управляет загрузкой, регистрацией и выполнением всех доступных инструментов.
Реализует паттерн Singleton для обеспечения единого реестра инструментов
во всем приложении.
"""

import logging
import importlib
import json
from typing import Dict, List, Any, Optional, Type
from aiogram import Bot

from app.tools.base_tool import Tool
from config.tools_config import TOOLS_CONFIG

logger = logging.getLogger(__name__)


class ToolRegistry:
    """
    Singleton-реестр для управления AI инструментами.

    Обеспечивает:
    - Загрузку инструментов из конфигурации
    - Формирование схем для OpenAI API
    - Выполнение инструментов по имени
    - Кеширование загруженных инструментов
    """

    _instance: Optional['ToolRegistry'] = None
    _initialized: bool = False

    def __new__(cls) -> 'ToolRegistry':
        """Реализация паттерна Singleton."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """Инициализация реестра (выполняется только один раз)."""
        if not self._initialized:
            self._tools: Dict[str, Tool] = {}
            self._openai_schemas: List[Dict[str, Any]] = []
            self._initialized = True
            logger.info("Инициализирован ToolRegistry")

    async def load_tools(self) -> None:
        """
        Загружает инструменты из конфигурации.

        Импортирует и регистрирует только те инструменты,
        у которых установлен флаг enabled: True.
        """
        logger.info("Начинаем загрузку инструментов...")
        loaded_count = 0

        for tool_name, tool_config in TOOLS_CONFIG.items():
            if not tool_config.get('enabled', False):
                logger.debug(f"Инструмент {tool_name} отключен, пропускаем")
                continue

            try:
                # Импортируем модуль
                module_path = tool_config['module']
                class_name = tool_config['class']

                logger.debug(f"Импортируем {module_path}.{class_name}")
                module = importlib.import_module(module_path)
                tool_class: Type[Tool] = getattr(module, class_name)

                # Создаем экземпляр инструмента
                tool_instance = tool_class()

                # Проверяем, что инструмент соответствует протоколу
                if not hasattr(tool_instance, 'name') or not hasattr(tool_instance, 'description'):
                    raise ValueError(f"Инструмент {class_name} не соответствует протоколу Tool")

                # Регистрируем инструмент
                self._tools[tool_instance.name] = tool_instance
                loaded_count += 1

                logger.info(f"Загружен инструмент: {tool_instance.name}")

            except Exception as e:
                logger.error(f"Ошибка загрузки инструмента {tool_name}: {e}")
                continue

        # Формируем схемы для OpenAI API
        self._build_openai_schemas()

        logger.info(f"Загружено инструментов: {loaded_count}/{len(TOOLS_CONFIG)}")

    def _build_openai_schemas(self) -> None:
        """Формирует схемы инструментов для OpenAI API."""
        self._openai_schemas = []

        for tool in self._tools.values():
            schema = {
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.schema
                }
            }
            self._openai_schemas.append(schema)

        logger.debug(f"Сформированы схемы для {len(self._openai_schemas)} инструментов")

    def get_openai_tools_schema(self) -> List[Dict[str, Any]]:
        """
        Возвращает схемы всех активных инструментов для OpenAI API.

        Returns:
            List[Dict[str, Any]]: Список схем в формате OpenAI Function Calling
        """
        return self._openai_schemas.copy()

    def get_enabled_tools(self) -> Dict[str, Tool]:
        """
        Возвращает словарь всех загруженных инструментов.

        Returns:
            Dict[str, Tool]: Словарь {имя_инструмента: экземпляр_инструмента}
        """
        return self._tools.copy()

    async def execute_tool(self, name: str, arguments: Dict[str, Any],
                          bot: Bot, chat_id: int) -> str:
        """
        Выполняет инструмент по имени с переданными аргументами.

        Args:
            name: Имя инструмента для выполнения
            arguments: Аргументы от LLM в формате JSON
            bot: Экземпляр Telegram бота
            chat_id: ID чата для взаимодействия

        Returns:
            str: Результат выполнения инструмента

        Raises:
            ValueError: Если инструмент не найден
            Exception: При ошибках выполнения инструмента
        """
        if name not in self._tools:
            available_tools = list(self._tools.keys())
            raise ValueError(f"Инструмент '{name}' не найден. Доступные: {available_tools}")

        tool = self._tools[name]

        try:
            logger.info(f"Выполняем инструмент {name} с аргументами: {arguments}")
            result = await tool.execute(bot, chat_id, **arguments)
            logger.debug(f"Инструмент {name} выполнен успешно")
            return result

        except Exception as e:
            error_msg = f"Ошибка выполнения инструмента {name}: {e}"
            logger.error(error_msg)
            raise Exception(error_msg) from e

    def is_tool_available(self, name: str) -> bool:
        """
        Проверяет, доступен ли инструмент с указанным именем.

        Args:
            name: Имя инструмента

        Returns:
            bool: True, если инструмент доступен
        """
        return name in self._tools

    def get_tools_count(self) -> int:
        """
        Возвращает количество загруженных инструментов.

        Returns:
            int: Количество активных инструментов
        """
        return len(self._tools)